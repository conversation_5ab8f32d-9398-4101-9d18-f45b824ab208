<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Fee extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'amount',
        'session',
        'group',
        'description',
        'due_date',
        'status'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'due_date' => 'date'
    ];

    // Relationships
    public function feePayments()
    {
        return $this->hasMany(FeePayment::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    public function scopeBySession($query, $session)
    {
        return $query->where('session', $session);
    }

    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Helper methods
    public function getTotalCollected()
    {
        return $this->feePayments()->where('status', 'Paid')->sum('amount_paid');
    }

    public function getCollectionPercentage()
    {
        $totalStudents = Student::active()
                               ->where('session', $this->session)
                               ->when($this->group, function ($q) {
                                   $q->where('group', $this->group);
                               })
                               ->count();
        
        if ($totalStudents == 0) {
            return 0;
        }
        
        $expectedTotal = $totalStudents * $this->amount;
        $collected = $this->getTotalCollected();
        
        return ($collected / $expectedTotal) * 100;
    }

    public function getPaidStudentsCount()
    {
        return $this->feePayments()
                   ->where('status', 'Paid')
                   ->distinct('student_id')
                   ->count();
    }

    public function getPendingStudentsCount()
    {
        $totalStudents = Student::active()
                               ->where('session', $this->session)
                               ->when($this->group, function ($q) {
                                   $q->where('group', $this->group);
                               })
                               ->count();
        
        return $totalStudents - $this->getPaidStudentsCount();
    }
}
