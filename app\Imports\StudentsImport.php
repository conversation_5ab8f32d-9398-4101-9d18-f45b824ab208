<?php

namespace App\Imports;

use App\Models\Student;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Illuminate\Support\Facades\Log;

class StudentsImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        // Skip empty rows
        if (empty($row['name'])) {
            return null;
        }

        try {
            $student = new Student();

            // Generate roll and registration if not provided
            $roll = !empty($row['roll']) ? $row['roll'] : $student->generateRoll();
            $regi = !empty($row['regi']) ? $row['regi'] : $student->generateRegistration();

            return new Student([
                'roll' => $roll,
                'name' => $row['name'],
                'regi' => $regi,
                'group' => $row['group'] ?? null,
                'fname' => $row['fname'] ?? null,
                'mname' => $row['mname'] ?? null,
                'gender' => $row['gender'] ?? null,
                'dob' => !empty($row['dob']) ? date('Y-m-d', strtotime($row['dob'])) : null,
                'session' => $row['session'] ?? config('education.academic.default_year', date('Y')),
                'sub1' => $row['sub1'] ?? null,
                'sub2' => $row['sub2'] ?? null,
                'sub3' => $row['sub3'] ?? null,
                '4th_sub' => $row['4th_sub'] ?? null,
                'phone' => $row['phone'] ?? null,
                'email' => $row['email'] ?? null,
                'address' => $row['address'] ?? null,
                'blood_group' => $row['blood_group'] ?? null,
                'religion' => $row['religion'] ?? null,
                'nationality' => $row['nationality'] ?? 'Bangladeshi',
                'admission_date' => !empty($row['admission_date']) ? date('Y-m-d', strtotime($row['admission_date'])) : null,
                'previous_school' => $row['previous_school'] ?? null,
                'previous_gpa' => $row['previous_gpa'] ?? null,
                'status' => $row['status'] ?? 'Active',
                'guardian_phone' => $row['guardian_phone'] ?? null,
                'guardian_email' => $row['guardian_email'] ?? null,
                'guardian_address' => $row['guardian_address'] ?? null,
                'emergency_contact' => $row['emergency_contact'] ?? null,
                'medical_info' => $row['medical_info'] ?? null,
                'notes' => $row['notes'] ?? null,
            ]);
        } catch (\Exception $e) {
            Log::error('Error importing student: ' . $e->getMessage(), $row);
            return null;
        }
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'roll' => 'nullable|string|max:50|unique:students,roll',
            'regi' => 'nullable|string|max:50|unique:students,regi',
            'email' => 'nullable|email|max:255|unique:students,email',
            'group' => 'nullable|string|in:' . implode(',', config('education.student.groups', [])),
            'gender' => 'nullable|in:Male,Female,Other',
            'dob' => 'nullable|date',
            'phone' => 'nullable|string|max:20',
            'blood_group' => 'nullable|string|in:A+,A-,B+,B-,AB+,AB-,O+,O-',
            'previous_gpa' => 'nullable|numeric|min:0|max:5',
            'status' => 'nullable|in:Active,Inactive,Graduated,Transferred',
        ];
    }

    /**
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            'name.required' => 'Student name is required in row :row',
            'roll.unique' => 'Roll number already exists in row :row',
            'regi.unique' => 'Registration number already exists in row :row',
            'email.unique' => 'Email already exists in row :row',
            'email.email' => 'Invalid email format in row :row',
            'group.in' => 'Invalid group in row :row',
            'gender.in' => 'Invalid gender in row :row',
            'blood_group.in' => 'Invalid blood group in row :row',
            'previous_gpa.min' => 'Previous GPA cannot be less than 0 in row :row',
            'previous_gpa.max' => 'Previous GPA cannot be more than 5 in row :row',
            'status.in' => 'Invalid status in row :row',
        ];
    }

    /**
     * @return int
     */
    public function startRow(): int
    {
        return 2;
    }

    /**
     * @param \Throwable $e
     * @param int $row
     */
    public function onError(\Throwable $e, int $row)
    {
        Log::error("Import error on row {$row}: " . $e->getMessage());
    }

    /**
     * @param \Maatwebsite\Excel\Validators\Failure[] $failures
     */
    public function onFailure(\Maatwebsite\Excel\Validators\Failure ...$failures)
    {
        foreach ($failures as $failure) {
            Log::error("Validation failed on row {$failure->row()}: " . implode(', ', $failure->errors()));
        }
    }
}
