<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExamResult extends Model
{
    use HasFactory;

    protected $fillable = [
        'exam_id',
        'student_id',
        'subject',
        'marks_obtained',
        'total_marks',
        'grade',
        'grade_point',
        'remarks'
    ];

    protected $casts = [
        'marks_obtained' => 'decimal:2',
        'total_marks' => 'decimal:2',
        'grade_point' => 'decimal:2'
    ];

    // Relationships
    public function exam()
    {
        return $this->belongsTo(Exam::class);
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    // Accessors
    public function getPercentageAttribute()
    {
        if ($this->total_marks > 0) {
            return ($this->marks_obtained / $this->total_marks) * 100;
        }
        return 0;
    }

    // Helper methods
    public function calculateGrade()
    {
        $percentage = $this->getPercentageAttribute();
        $grades = config('education.exam.grades');
        
        foreach ($grades as $grade => $range) {
            if ($percentage >= $range['min'] && $percentage <= $range['max']) {
                $this->grade = $grade;
                $this->grade_point = $range['point'];
                break;
            }
        }
        
        return $this;
    }

    public function isPassed($passingMarks = 33)
    {
        return $this->marks_obtained >= $passingMarks;
    }
}
