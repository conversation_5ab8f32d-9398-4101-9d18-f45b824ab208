/* SKUL Education Management System - Main Styles */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-width: 260px;
    --navbar-height: 70px;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    color: #2c3e50;
    line-height: 1.6;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    z-index: 1000;
    transition: var(--transition);
    overflow-y: auto;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
    color: white;
    font-weight: 700;
    margin-bottom: 5px;
}

.sidebar-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.sidebar-menu {
    padding: 20px 0;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu li {
    margin: 5px 15px;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.sidebar-menu i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    padding: 20px;
    transition: var(--transition);
}

/* Navbar */
.navbar {
    background: white;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    padding: 15px 25px;
}

.navbar-brand {
    font-weight: 700;
    color: var(--dark-color);
}

.navbar-nav .nav-link {
    color: var(--dark-color);
    font-weight: 500;
    padding: 8px 15px;
    border-radius: 8px;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background-color: var(--light-color);
}

/* Welcome Card */
.welcome-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 30px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.welcome-icon {
    font-size: 4rem;
    opacity: 0.3;
}

/* Statistics Cards */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: none;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.stats-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.stats-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.stats-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    color: white;
}

.stats-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stats-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 1rem;
    margin-bottom: 5px;
    opacity: 0.9;
}

.stats-detail {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Chart Cards */
.chart-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
}

.chart-card .card-header {
    background: transparent;
    border-bottom: 1px solid #eee;
    padding: 20px 25px;
}

.chart-card .card-body {
    padding: 25px;
}

/* Activity Cards */
.activity-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
}

.activity-card .card-header {
    background: transparent;
    border-bottom: 1px solid #eee;
    padding: 20px 25px;
}

.activity-card .card-body {
    padding: 25px;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    margin-right: 15px;
}

.activity-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.activity-desc {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.activity-time {
    font-size: 0.8rem;
    color: #adb5bd;
}

.activity-status {
    margin-left: 15px;
}

/* Quick Action Buttons */
.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    height: 120px;
}

.quick-action-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    color: var(--primary-color);
}

.quick-action-btn i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.quick-action-btn span {
    font-weight: 600;
    text-align: center;
    font-size: 0.9rem;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid #eee;
    padding: 20px 25px;
}

.card-body {
    padding: 25px;
}

.card-title {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    border: none;
}

/* Forms */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Tables */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 15px;
}

.table td {
    border: none;
    padding: 15px;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Loading Spinner */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 15px;
    }
    
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .welcome-icon {
        font-size: 2.5rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .stats-icon {
        font-size: 2rem;
    }
    
    .quick-action-btn {
        height: 100px;
        padding: 20px 15px;
    }
    
    .quick-action-btn i {
        font-size: 2rem;
        margin-bottom: 8px;
    }
    
    .quick-action-btn span {
        font-size: 0.8rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.bg-gradient-primary { background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important; }
.border-radius { border-radius: var(--border-radius) !important; }
.box-shadow { box-shadow: var(--box-shadow) !important; }
