<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StudentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $studentId = $this->route('student') ? $this->route('student')->id : null;

        return [
            'roll' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('students', 'roll')->ignore($studentId)
            ],
            'name' => 'required|string|max:255',
            'regi' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('students', 'regi')->ignore($studentId)
            ],
            'group' => 'nullable|string|in:' . implode(',', config('education.student.groups', [])),
            'fname' => 'nullable|string|max:255',
            'mname' => 'nullable|string|max:255',
            'gender' => 'nullable|in:Male,Female,Other',
            'dob' => 'nullable|date|before:today',
            'session' => 'nullable|string|max:20',
            'sub1' => 'nullable|string|max:100',
            'sub2' => 'nullable|string|max:100',
            'sub3' => 'nullable|string|max:100',
            '4th_sub' => 'nullable|string|max:100',
            'picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^([0-9\s\-\+\(\)]*)$/'
            ],
            'email' => [
                'nullable',
                'email',
                'max:255',
                Rule::unique('students', 'email')->ignore($studentId)
            ],
            'address' => 'nullable|string|max:500',
            'blood_group' => 'nullable|string|in:A+,A-,B+,B-,AB+,AB-,O+,O-',
            'religion' => 'nullable|string|max:50',
            'nationality' => 'nullable|string|max:50',
            'admission_date' => 'nullable|date',
            'previous_school' => 'nullable|string|max:255',
            'previous_gpa' => 'nullable|numeric|min:0|max:5',
            'status' => 'nullable|in:Active,Inactive,Graduated,Transferred',
            'guardian_phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^([0-9\s\-\+\(\)]*)$/'
            ],
            'guardian_email' => 'nullable|email|max:255',
            'guardian_address' => 'nullable|string|max:500',
            'emergency_contact' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^([0-9\s\-\+\(\)]*)$/'
            ],
            'medical_info' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000'
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Student name is required.',
            'name.max' => 'Student name cannot exceed 255 characters.',
            'roll.unique' => 'This roll number is already taken.',
            'regi.unique' => 'This registration number is already taken.',
            'email.unique' => 'This email address is already taken.',
            'email.email' => 'Please enter a valid email address.',
            'dob.before' => 'Date of birth must be before today.',
            'picture.image' => 'Picture must be an image file.',
            'picture.mimes' => 'Picture must be a file of type: jpeg, png, jpg, gif.',
            'picture.max' => 'Picture size cannot exceed 2MB.',
            'phone.regex' => 'Please enter a valid phone number.',
            'guardian_phone.regex' => 'Please enter a valid guardian phone number.',
            'emergency_contact.regex' => 'Please enter a valid emergency contact number.',
            'previous_gpa.min' => 'Previous GPA cannot be less than 0.',
            'previous_gpa.max' => 'Previous GPA cannot be more than 5.',
            'group.in' => 'Please select a valid group.',
            'gender.in' => 'Please select a valid gender.',
            'blood_group.in' => 'Please select a valid blood group.',
            'status.in' => 'Please select a valid status.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'fname' => 'father\'s name',
            'mname' => 'mother\'s name',
            'dob' => 'date of birth',
            'regi' => 'registration number',
            'sub1' => 'first subject',
            'sub2' => 'second subject',
            'sub3' => 'third subject',
            '4th_sub' => 'fourth subject',
            'guardian_phone' => 'guardian phone',
            'guardian_email' => 'guardian email',
            'guardian_address' => 'guardian address',
            'emergency_contact' => 'emergency contact',
            'medical_info' => 'medical information',
            'previous_gpa' => 'previous GPA'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if (empty($this->session)) {
            $this->merge([
                'session' => config('education.academic.default_year', date('Y'))
            ]);
        }

        if (empty($this->nationality)) {
            $this->merge([
                'nationality' => 'Bangladeshi'
            ]);
        }

        if (empty($this->status)) {
            $this->merge([
                'status' => 'Active'
            ]);
        }

        // Clean phone numbers
        if ($this->phone) {
            $this->merge([
                'phone' => preg_replace('/[^0-9+]/', '', $this->phone)
            ]);
        }

        if ($this->guardian_phone) {
            $this->merge([
                'guardian_phone' => preg_replace('/[^0-9+]/', '', $this->guardian_phone)
            ]);
        }

        if ($this->emergency_contact) {
            $this->merge([
                'emergency_contact' => preg_replace('/[^0-9+]/', '', $this->emergency_contact)
            ]);
        }
    }
}
