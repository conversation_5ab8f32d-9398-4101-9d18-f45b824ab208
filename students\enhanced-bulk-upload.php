<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

// Increase limits for large files
ini_set('memory_limit', '1G');
ini_set('max_execution_time', 600); // 10 minutes
set_time_limit(600);

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload') {
    $result = handleEnhancedBulkUpload();
    $message = $result['message'];
    $messageType = $result['type'];
}

function handleEnhancedBulkUpload() {
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        return ['message' => 'ফাইল আপলোড করতে সমস্যা হয়েছে।', 'type' => 'error'];
    }

    $file = $_FILES['csv_file'];
    $allowedTypes = ['text/csv', 'application/csv', 'text/plain'];
    $fileType = mime_content_type($file['tmp_name']);
    
    if (!in_array($fileType, $allowedTypes) && !str_ends_with($file['name'], '.csv')) {
        return ['message' => 'শুধুমাত্র CSV ফাইল আপলোড করুন।', 'type' => 'error'];
    }

    $maxSize = 50 * 1024 * 1024; // 50MB
    if ($file['size'] > $maxSize) {
        return ['message' => 'ফাইলের আকার ৫০ MB এর বেশি হতে পারবে না।', 'type' => 'error'];
    }

    return processEnhancedCSV($file['tmp_name']);
}

function processEnhancedCSV($filePath) {
    $handle = fopen($filePath, 'r');
    if (!$handle) {
        return ['message' => 'ফাইল পড়তে সমস্যা হয়েছে।', 'type' => 'error'];
    }

    // Check for BOM and remove it
    $bom = fread($handle, 3);
    if ($bom !== "\xEF\xBB\xBF") {
        rewind($handle);
    }

    // Read headers
    $headers = fgetcsv($handle);
    if (!$headers) {
        fclose($handle);
        return ['message' => 'CSV ফাইলে হেডার পাওয়া যায়নি।', 'type' => 'error'];
    }

    // Clean headers from any remaining BOM or special characters
    $headers = array_map(function($header) {
        return trim(preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $header));
    }, $headers);

    // Map headers
    $headerMap = [];
    foreach ($headers as $index => $header) {
        $headerMap[trim(strtolower($header))] = $index;
    }

    // Debug: Show headers found
    $headerDebug = "Headers found: " . implode(', ', array_keys($headerMap));

    // Check for name column
    if (!isset($headerMap['name'])) {
        fclose($handle);
        return ['message' => "প্রয়োজনীয় কলাম 'name' পাওয়া যায়নি।\n$headerDebug", 'type' => 'error'];
    }

    $successCount = 0;
    $errorCount = 0;
    $duplicateCount = 0;
    $skippedCount = 0;
    $errors = [];
    $rowNumber = 1;
    $batchSize = 500;
    $processedInBatch = 0;
    $debugInfo = [];

    try {
        // Disable foreign key checks for faster insertion
        executeQuery("SET FOREIGN_KEY_CHECKS = 0");
        executeQuery("START TRANSACTION");

        while (($row = fgetcsv($handle)) !== false) {
            $rowNumber++;

            // Debug: Log first few rows
            if ($rowNumber <= 5) {
                $debugInfo[] = "Row $rowNumber: " . implode(' | ', array_slice($row, 0, 5));
            }

            // Skip empty rows
            if (empty(array_filter($row))) {
                $skippedCount++;
                continue;
            }

            // Map data
            $studentData = [];
            foreach ($headerMap as $field => $index) {
                $value = isset($row[$index]) ? trim($row[$index]) : '';
                $studentData[$field] = $value;
            }

            // Debug: Log first student data
            if ($rowNumber == 2) {
                $debugInfo[] = "First student data: " . json_encode($studentData);
            }

            // Validate name
            if (empty($studentData['name'])) {
                $errorCount++;
                if ($errorCount <= 10) {
                    $errors[] = "সারি $rowNumber: নাম প্রয়োজন। Data: " . json_encode(array_slice($studentData, 0, 3));
                }
                continue;
            }

            // Generate registration if not provided
            if (empty($studentData['regi'])) {
                $studentData['regi'] = 'REG' . date('Y') . str_pad($rowNumber, 6, '0', STR_PAD_LEFT);
            }

            // Set defaults
            $studentData['session'] = $studentData['session'] ?? date('Y');
            $studentData['status'] = 'Active';
            $studentData['nationality'] = $studentData['nationality'] ?? 'Bangladeshi';

            // Debug: Log processing attempt
            if ($rowNumber <= 10 || $rowNumber % 100 == 0) {
                $debugInfo[] = "Processing row $rowNumber: {$studentData['name']} (Regi: {$studentData['regi']})";
            }

            // Quick insert with duplicate handling
            $result = quickInsertStudent($studentData, $rowNumber);

            if ($result['success']) {
                $successCount++;
                if ($successCount <= 5) {
                    $debugInfo[] = "✓ Success $successCount: {$studentData['name']}";
                }
            } elseif ($result['duplicate']) {
                $duplicateCount++;
                if ($duplicateCount <= 5) {
                    $debugInfo[] = "⚠ Duplicate $duplicateCount: {$studentData['name']} (Regi: {$studentData['regi']})";
                }
            } else {
                $errorCount++;
                if ($errorCount <= 10) {
                    $errors[] = $result['error'];
                    $debugInfo[] = "✗ Error $errorCount: {$studentData['name']} - {$result['error']}";
                }
            }

            $processedInBatch++;
            
            // Commit batch
            if ($processedInBatch >= $batchSize) {
                executeQuery("COMMIT");
                executeQuery("START TRANSACTION");
                $processedInBatch = 0;
                
                // Memory cleanup
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
        }

        // Final commit
        executeQuery("COMMIT");
        executeQuery("SET FOREIGN_KEY_CHECKS = 1");

    } catch (Exception $e) {
        executeQuery("ROLLBACK");
        executeQuery("SET FOREIGN_KEY_CHECKS = 1");
        fclose($handle);
        return ['message' => 'ডেটাবেস ত্রুটি: ' . $e->getMessage(), 'type' => 'error'];
    }

    fclose($handle);

    $totalProcessed = $successCount + $duplicateCount + $errorCount;
    $message = "আপলোড সম্পন্ন!\n";
    $message .= "মোট সারি: $rowNumber\n";
    $message .= "মোট প্রক্রিয়াকৃত: $totalProcessed\n";
    $message .= "সফল: $successCount\n";
    $message .= "ডুপ্লিকেট (এড়ানো): $duplicateCount\n";
    $message .= "ব্যর্থ: $errorCount\n";
    $message .= "খালি সারি এড়ানো: $skippedCount";

    if (!empty($debugInfo)) {
        $message .= "\n\nDebug Info:\n" . implode("\n", $debugInfo);
    }

    if (!empty($errors)) {
        $message .= "\n\nপ্রথম কয়েকটি ত্রুটি:\n" . implode("\n", $errors);
        if ($errorCount > 10) {
            $message .= "\n... এবং আরো " . ($errorCount - 10) . " টি ত্রুটি।";
        }
    }

    return [
        'message' => $message,
        'type' => $successCount > 0 ? 'success' : 'warning'
    ];
}

function quickInsertStudent($data, $rowNumber) {
    try {
        // Validate email if provided
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return ['success' => false, 'duplicate' => false, 'error' => "সারি $rowNumber: ইমেইল সঠিক নয়। ({$data['email']})"];
        }

        // Validate date if provided
        if (!empty($data['dob'])) {
            $dob = date('Y-m-d', strtotime($data['dob']));
            if (!$dob || $dob === '1970-01-01') {
                $data['dob'] = null;
            } else {
                $data['dob'] = $dob;
            }
        }

        // Check for existing registration number before insert
        if (!empty($data['regi'])) {
            $existing = fetchRow("SELECT id, name FROM students WHERE regi = ?", [$data['regi']]);
            if ($existing) {
                return ['success' => false, 'duplicate' => true, 'error' => "সারি $rowNumber: রেজি '{$data['regi']}' already exists for {$existing['name']}"];
            }
        }

        // Regular insert to get proper error messages
        $sql = "INSERT INTO students (roll, name, regi, `group`, fname, mname, gender, dob, session,
                sub1, sub2, sub3, `4th_sub`, phone, email, address, blood_group, religion,
                nationality, admission_date, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $params = [
            $data['roll'] ?? null, $data['name'], $data['regi'], $data['group'] ?? null,
            $data['fname'] ?? null, $data['mname'] ?? null, $data['gender'] ?? null,
            $data['dob'] ?? null, $data['session'], $data['sub1'] ?? null,
            $data['sub2'] ?? null, $data['sub3'] ?? null, $data['4th_sub'] ?? null,
            $data['phone'] ?? null, $data['email'] ?? null, $data['address'] ?? null,
            $data['blood_group'] ?? null, $data['religion'] ?? null, $data['nationality'],
            $data['admission_date'] ?? null, $data['status']
        ];

        global $pdo;
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($params);

        // Check if execution was successful
        if ($result) {
            $affectedRows = $stmt->rowCount();

            if ($affectedRows > 0) {
                return ['success' => true, 'duplicate' => false];
            } else {
                return ['success' => false, 'duplicate' => false, 'error' => "সারি $rowNumber: Insert failed - no rows affected"];
            }
        } else {
            $errorInfo = $stmt->errorInfo();
            return ['success' => false, 'duplicate' => false, 'error' => "সারি $rowNumber: Execute failed - " . $errorInfo[2]];
        }

    } catch (PDOException $e) {
        $errorMsg = $e->getMessage();
        $errorCode = $e->getCode();

        // Handle duplicate entry errors
        if ($errorCode == 23000 || strpos($errorMsg, 'Duplicate entry') !== false) {
            if (strpos($errorMsg, 'regi') !== false) {
                return ['success' => false, 'duplicate' => true, 'error' => "সারি $rowNumber: Duplicate registration '{$data['regi']}'"];
            } elseif (strpos($errorMsg, 'email') !== false) {
                return ['success' => false, 'duplicate' => true, 'error' => "সারি $rowNumber: Duplicate email '{$data['email']}'"];
            } else {
                return ['success' => false, 'duplicate' => true, 'error' => "সারি $rowNumber: Duplicate entry"];
            }
        }

        return ['success' => false, 'duplicate' => false, 'error' => "সারি $rowNumber: PDO Error ($errorCode): " . $errorMsg];
    } catch (Exception $e) {
        return ['success' => false, 'duplicate' => false, 'error' => "সারি $rowNumber: General Error: " . $e->getMessage()];
    }
}

// Get user data for navbar
$user = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Bulk Upload - Students</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-upload me-2"></i>Enhanced Bulk Upload
                    </h1>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : ($messageType === 'warning' ? 'warning' : 'success'); ?> alert-dismissible fade show">
                        <pre style="white-space: pre-wrap; margin: 0;"><?php echo htmlspecialchars($message); ?></pre>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-csv me-2"></i>CSV ফাইল আপলোড করুন
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="upload">
                                    
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">CSV ফাইল নির্বাচন করুন</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                        <div class="form-text">সর্বোচ্চ ফাইল সাইজ: ৫০ MB</div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-2"></i>আপলোড করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Enhanced Features
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Large file support (50MB)</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Batch processing</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Duplicate handling</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Memory optimization</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Better error reporting</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <a href="index.php" class="btn btn-outline-primary">
                                <i class="fas fa-list me-2"></i>ছাত্রদের তালিকা
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
