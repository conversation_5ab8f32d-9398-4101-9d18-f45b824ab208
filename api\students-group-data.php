<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    errorResponse('Unauthorized', 401);
}

try {
    // Get students by group data
    $sql = "SELECT `group`, COUNT(*) as total 
            FROM students 
            WHERE status = 'Active' AND `group` IS NOT NULL 
            GROUP BY `group`";
    
    $results = fetchAll($sql);
    
    $labels = [];
    $data = [];
    
    if (!empty($results)) {
        foreach ($results as $row) {
            $labels[] = $row['group'];
            $data[] = intval($row['total']);
        }
    } else {
        // Sample data if no students exist
        $labels = ['বিজ্ঞান', 'ব্যবসায়', 'মানবিক'];
        $data = [3, 1, 1]; // Based on sample students
    }
    
    $chartData = [
        'labels' => $labels,
        'data' => $data
    ];
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($chartData);
    
} catch (Exception $e) {
    errorResponse('Error fetching students group data: ' . $e->getMessage());
}
?>
