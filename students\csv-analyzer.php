<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$analysis = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    $file = $_FILES['csv_file'];
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $analysis = analyzeCSV($file['tmp_name']);
    } else {
        $analysis = 'File upload error: ' . $file['error'];
    }
}

function analyzeCSV($filePath) {
    $handle = fopen($filePath, 'r');
    if (!$handle) {
        return 'Cannot open file';
    }
    
    $analysis = [];
    $analysis[] = "=== CSV File Analysis ===\n";
    
    // File info
    $fileSize = filesize($filePath);
    $analysis[] = "File size: " . number_format($fileSize) . " bytes";
    
    // Check encoding
    $content = file_get_contents($filePath);
    $encoding = mb_detect_encoding($content, ['UTF-8', 'UTF-16', 'Windows-1252', 'ISO-8859-1'], true);
    $analysis[] = "Detected encoding: " . ($encoding ?: 'Unknown');
    
    // Check BOM
    $bom = substr($content, 0, 3);
    if ($bom === "\xEF\xBB\xBF") {
        $analysis[] = "BOM detected: UTF-8 BOM found";
    } else {
        $analysis[] = "BOM: None detected";
    }
    
    // Reopen file for line-by-line analysis
    rewind($handle);
    
    // Skip BOM if present
    if ($bom === "\xEF\xBB\xBF") {
        fread($handle, 3);
    }
    
    $lineCount = 0;
    $headers = null;
    $sampleRows = [];
    
    while (($row = fgetcsv($handle)) !== false && $lineCount < 10) {
        $lineCount++;
        
        if ($lineCount === 1) {
            $headers = $row;
            $analysis[] = "\nHeaders found (" . count($headers) . " columns):";
            foreach ($headers as $i => $header) {
                $cleanHeader = trim(preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $header));
                $analysis[] = "  [$i] '$header' -> '$cleanHeader'";
            }
        } else {
            $sampleRows[] = $row;
        }
    }
    
    // Count total lines
    rewind($handle);
    if ($bom === "\xEF\xBB\xBF") {
        fread($handle, 3);
    }
    
    $totalLines = 0;
    while (fgetcsv($handle) !== false) {
        $totalLines++;
    }
    
    $analysis[] = "\nTotal lines: $totalLines";
    $analysis[] = "Data rows: " . ($totalLines - 1);
    
    // Show sample data
    $analysis[] = "\nFirst 5 data rows:";
    foreach ($sampleRows as $i => $row) {
        $analysis[] = "Row " . ($i + 2) . ": " . implode(' | ', array_slice($row, 0, 5));
    }
    
    // Check for name column
    if ($headers) {
        $headerMap = [];
        foreach ($headers as $index => $header) {
            $cleanHeader = trim(strtolower(preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $header)));
            $headerMap[$cleanHeader] = $index;
        }
        
        $analysis[] = "\nCleaned header mapping:";
        foreach ($headerMap as $clean => $index) {
            $analysis[] = "  '$clean' -> column $index";
        }
        
        if (isset($headerMap['name'])) {
            $analysis[] = "\n✓ 'name' column found at index: " . $headerMap['name'];
        } else {
            $analysis[] = "\n✗ 'name' column NOT found!";
            $analysis[] = "Available columns: " . implode(', ', array_keys($headerMap));
        }
    }
    
    fclose($handle);
    
    return implode("\n", $analysis);
}

// Get user data for navbar
$user = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV Analyzer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-search me-2"></i>CSV File Analyzer
                    </h1>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-csv me-2"></i>Analyze CSV File
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">Select CSV File</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Analyze
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <?php if ($analysis): ?>
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>Analysis Results
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <pre style="white-space: pre-wrap; font-size: 12px; max-height: 500px; overflow-y: auto;"><?php echo htmlspecialchars($analysis); ?></pre>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="enhanced-bulk-upload.php" class="btn btn-outline-primary">
                        <i class="fas fa-upload me-2"></i>Back to Upload
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-list me-2"></i>Students List
                    </a>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
