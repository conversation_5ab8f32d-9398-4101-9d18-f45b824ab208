@extends('layouts.admin')

@section('title', 'Students Management')
@section('page-title', 'Students')

@section('content')
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">Students Management</h4>
                <p class="text-muted">Manage student information and records</p>
            </div>
            <div class="btn-group">
                <a href="{{ route('admin.students.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Add Student
                </a>
                <a href="{{ route('admin.students.bulk-upload') }}" class="btn btn-success">
                    <i class="fas fa-upload me-1"></i> Bulk Upload
                </a>
                <a href="{{ route('admin.students.export') }}" class="btn btn-info">
                    <i class="fas fa-download me-1"></i> Export
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.students.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request('search') }}" placeholder="Name, Roll, Registration...">
            </div>
            <div class="col-md-2">
                <label for="session" class="form-label">Session</label>
                <select class="form-select" id="session" name="session">
                    <option value="">All Sessions</option>
                    @foreach($sessions as $session)
                        <option value="{{ $session }}" {{ request('session') == $session ? 'selected' : '' }}>
                            {{ $session }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label for="group" class="form-label">Group</label>
                <select class="form-select" id="group" name="group">
                    <option value="">All Groups</option>
                    @foreach($groups as $group)
                        <option value="{{ $group }}" {{ request('group') == $group ? 'selected' : '' }}>
                            {{ $group }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    @foreach($statuses as $status)
                        <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                            {{ $status }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> Filter
                    </button>
                    <a href="{{ route('admin.students.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Students Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-user-graduate me-2"></i>
            Students List ({{ $students->total() }} total)
        </h5>
    </div>
    <div class="card-body">
        @if($students->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Photo</th>
                            <th>Roll</th>
                            <th>Name</th>
                            <th>Registration</th>
                            <th>Group</th>
                            <th>Session</th>
                            <th>Contact</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($students as $student)
                            <tr>
                                <td>
                                    <img src="{{ $student->picture_url }}" alt="{{ $student->name }}" 
                                         class="avatar">
                                </td>
                                <td>
                                    <strong>{{ $student->roll }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ $student->name }}</strong>
                                        @if($student->gender)
                                            <small class="text-muted d-block">{{ $student->gender }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>{{ $student->regi }}</td>
                                <td>
                                    @if($student->group)
                                        <span class="badge bg-info">{{ $student->group }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>{{ $student->session }}</td>
                                <td>
                                    @if($student->phone)
                                        <small class="d-block">{{ $student->phone }}</small>
                                    @endif
                                    @if($student->email)
                                        <small class="text-muted d-block">{{ $student->email }}</small>
                                    @endif
                                </td>
                                <td>
                                    @php
                                        $statusColors = [
                                            'Active' => 'success',
                                            'Inactive' => 'secondary',
                                            'Graduated' => 'primary',
                                            'Transferred' => 'warning'
                                        ];
                                    @endphp
                                    <span class="badge bg-{{ $statusColors[$student->status] ?? 'secondary' }}">
                                        {{ $student->status }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('admin.students.show', $student) }}" 
                                           class="btn btn-outline-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.students.edit', $student) }}" 
                                           class="btn btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.students.destroy', $student) }}" 
                                              method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" 
                                                    title="Delete" onclick="return confirmDelete()">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    <small class="text-muted">
                        Showing {{ $students->firstItem() }} to {{ $students->lastItem() }} 
                        of {{ $students->total() }} results
                    </small>
                </div>
                <div>
                    {{ $students->appends(request()->query())->links() }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No students found</h5>
                <p class="text-muted">
                    @if(request()->hasAny(['search', 'session', 'group', 'status']))
                        Try adjusting your filters or 
                        <a href="{{ route('admin.students.index') }}">clear all filters</a>
                    @else
                        Start by <a href="{{ route('admin.students.create') }}">adding a new student</a>
                    @endif
                </p>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-submit form on filter change
    $('#session, #group, #status').on('change', function() {
        $(this).closest('form').submit();
    });
</script>
@endpush
