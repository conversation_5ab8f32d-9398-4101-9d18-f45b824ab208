<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Http\Requests\StudentRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\StudentsImport;
use App\Exports\StudentsExport;

class StudentController extends Controller
{
    public function index(Request $request)
    {
        $query = Student::with(['examResults', 'feePayments']);

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by session
        if ($request->filled('session')) {
            $query->bySession($request->session);
        }

        // Filter by group
        if ($request->filled('group')) {
            $query->byGroup($request->group);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $students = $query->latest()->paginate(20);

        // Get filter options
        $sessions = Student::distinct()->pluck('session')->filter()->sort();
        $groups = config('education.student.groups');
        $statuses = ['Active', 'Inactive', 'Graduated', 'Transferred'];

        return view('admin.students.index', compact('students', 'sessions', 'groups', 'statuses'));
    }

    public function create()
    {
        $groups = config('education.student.groups');
        $subjects = config('education.student.subjects');
        $genders = config('education.student.genders');
        
        return view('admin.students.create', compact('groups', 'subjects', 'genders'));
    }

    public function store(StudentRequest $request)
    {
        $data = $request->validated();

        // Handle picture upload
        if ($request->hasFile('picture')) {
            $data['picture'] = $request->file('picture')->store('students', 'public');
        }

        // Generate roll and registration if not provided
        if (empty($data['roll'])) {
            $student = new Student();
            $data['roll'] = $student->generateRoll();
        }

        if (empty($data['regi'])) {
            $student = new Student();
            $data['regi'] = $student->generateRegistration();
        }

        $student = Student::create($data);

        return redirect()->route('admin.students.index')
                        ->with('success', 'Student created successfully.');
    }

    public function show(Student $student)
    {
        $student->load(['examResults.exam', 'feePayments.fee']);
        
        return view('admin.students.show', compact('student'));
    }

    public function edit(Student $student)
    {
        $groups = config('education.student.groups');
        $subjects = config('education.student.subjects');
        $genders = config('education.student.genders');
        
        return view('admin.students.edit', compact('student', 'groups', 'subjects', 'genders'));
    }

    public function update(StudentRequest $request, Student $student)
    {
        $data = $request->validated();

        // Handle picture upload
        if ($request->hasFile('picture')) {
            // Delete old picture
            if ($student->picture) {
                Storage::disk('public')->delete($student->picture);
            }
            $data['picture'] = $request->file('picture')->store('students', 'public');
        }

        $student->update($data);

        return redirect()->route('admin.students.index')
                        ->with('success', 'Student updated successfully.');
    }

    public function destroy(Student $student)
    {
        // Delete picture if exists
        if ($student->picture) {
            Storage::disk('public')->delete($student->picture);
        }

        $student->delete();

        return redirect()->route('admin.students.index')
                        ->with('success', 'Student deleted successfully.');
    }

    public function bulkUpload()
    {
        return view('admin.students.bulk-upload');
    }

    public function importStudents(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:10240'
        ]);

        try {
            Excel::import(new StudentsImport, $request->file('file'));
            
            return redirect()->route('admin.students.index')
                            ->with('success', 'Students imported successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                            ->with('error', 'Error importing students: ' . $e->getMessage());
        }
    }

    public function exportStudents(Request $request)
    {
        $filters = $request->only(['session', 'group', 'status']);
        
        return Excel::download(new StudentsExport($filters), 'students.xlsx');
    }

    public function downloadTemplate()
    {
        $headers = [
            'roll', 'name', 'regi', 'group', 'fname', 'mname', 'gender', 
            'dob', 'session', 'sub1', 'sub2', 'sub3', '4th_sub', 'phone', 
            'email', 'address', 'blood_group', 'religion', 'admission_date'
        ];

        $filename = 'student_import_template.csv';
        
        $handle = fopen('php://output', 'w');
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        fputcsv($handle, $headers);
        
        // Add sample data
        fputcsv($handle, [
            'STU24001', 'John Doe', 'REG2024001', 'Science', 'Father Name', 
            'Mother Name', 'Male', '2005-01-15', '2024', 'Physics', 
            'Chemistry', 'Mathematics', 'Biology', '01712345678', 
            '<EMAIL>', 'Sample Address', 'A+', 'Islam', '2024-01-01'
        ]);
        
        fclose($handle);
        exit;
    }

    public function profile(Student $student)
    {
        $student->load([
            'examResults.exam', 
            'feePayments.fee',
            'feePayments.collector'
        ]);
        
        return view('admin.students.profile', compact('student'));
    }

    public function toggleStatus(Student $student)
    {
        $newStatus = $student->status === 'Active' ? 'Inactive' : 'Active';
        $student->update(['status' => $newStatus]);

        return redirect()->back()
                        ->with('success', 'Student status updated successfully.');
    }
}
