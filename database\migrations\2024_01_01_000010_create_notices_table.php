<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notices', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('content');
            $table->string('type'); // General, Academic, Examination, etc.
            $table->string('priority'); // Low, Medium, High, Urgent
            $table->date('publish_date');
            $table->date('expiry_date')->nullable();
            $table->string('target_audience')->nullable(); // Students, Teachers, All, etc.
            $table->string('attachment')->nullable(); // File path
            $table->foreignId('created_by')->constrained('teachers')->onDelete('cascade');
            $table->boolean('is_published')->default(false);
            $table->boolean('send_notification')->default(false);
            $table->enum('status', ['Draft', 'Published', 'Expired', 'Archived'])->default('Draft');
            $table->timestamps();
            
            $table->index(['type', 'priority', 'publish_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notices');
    }
};
