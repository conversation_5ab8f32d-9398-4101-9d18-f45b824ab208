<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error - SKUL Education System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 100%;
            margin: 20px;
            overflow: hidden;
        }
        .error-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .error-body {
            padding: 30px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-header">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <h1>Oops! Something went wrong</h1>
            <p class="mb-0">একটি সমস্যা হয়েছে</p>
        </div>
        
        <div class="error-body">
            <?php
            $error_message = $_GET['message'] ?? 'Unknown error occurred';
            $error_code = $_GET['code'] ?? '500';
            ?>
            
            <div class="alert alert-danger">
                <h5><i class="fas fa-bug me-2"></i>Error Details:</h5>
                <p><strong>Code:</strong> <?php echo htmlspecialchars($error_code); ?></p>
                <p><strong>Message:</strong> <?php echo htmlspecialchars($error_message); ?></p>
            </div>
            
            <div class="alert alert-info">
                <h6><i class="fas fa-lightbulb me-2"></i>Possible Solutions:</h6>
                <ul>
                    <li>Make sure XAMPP is running (Apache + MySQL)</li>
                    <li>Check if you're logged in properly</li>
                    <li>Try refreshing the page</li>
                    <li>Clear your browser cache</li>
                    <li>Check database connection</li>
                </ul>
            </div>
            
            <div class="text-center">
                <a href="index.php" class="btn btn-primary me-2">
                    <i class="fas fa-home me-1"></i>Go Home
                </a>
                <a href="login.php" class="btn btn-success me-2">
                    <i class="fas fa-sign-in-alt me-1"></i>Login
                </a>
                <a href="test-connection.php" class="btn btn-info">
                    <i class="fas fa-database me-1"></i>Test Connection
                </a>
            </div>
            
            <div class="mt-4 pt-3 border-top text-center">
                <small class="text-muted">
                    If the problem persists, please check the XAMPP control panel and ensure MySQL is running.
                </small>
            </div>
        </div>
    </div>
</body>
</html>
