<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$message = '';
$messageType = '';
$debugInfo = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $debugInfo[] = "POST Request received";
    $debugInfo[] = "POST data: " . json_encode($_POST);
    $debugInfo[] = "FILES data: " . json_encode(array_keys($_FILES));
    
    if (isset($_POST['action'])) {
        $debugInfo[] = "Action: " . $_POST['action'];
        
        if ($_POST['action'] === 'upload') {
            $debugInfo[] = "Processing upload...";
            
            if (!isset($_FILES['csv_file'])) {
                $message = "No file uploaded";
                $messageType = 'error';
            } else {
                $file = $_FILES['csv_file'];
                $debugInfo[] = "File info: " . json_encode($file);
                
                if ($file['error'] !== UPLOAD_ERR_OK) {
                    $message = "File upload error: " . $file['error'];
                    $messageType = 'error';
                } else {
                    // Process file
                    $result = processDebugCSV($file['tmp_name']);
                    $message = $result['message'];
                    $messageType = $result['type'];
                    if (isset($result['debug'])) {
                        $debugInfo = array_merge($debugInfo, $result['debug']);
                    }
                }
            }
        }
    } else {
        $debugInfo[] = "No action specified";
    }
} else {
    $debugInfo[] = "No POST request";
}

function processDebugCSV($filePath) {
    $debug = [];
    $debug[] = "Processing file: $filePath";
    
    if (!file_exists($filePath)) {
        return ['message' => 'File does not exist', 'type' => 'error', 'debug' => $debug];
    }
    
    $debug[] = "File size: " . filesize($filePath) . " bytes";
    
    $handle = fopen($filePath, 'r');
    if (!$handle) {
        return ['message' => 'Cannot open file', 'type' => 'error', 'debug' => $debug];
    }
    
    // Check for BOM
    $bom = fread($handle, 3);
    if ($bom !== "\xEF\xBB\xBF") {
        rewind($handle);
        $debug[] = "No BOM detected";
    } else {
        $debug[] = "BOM detected and removed";
    }
    
    // Read headers
    $headers = fgetcsv($handle);
    if (!$headers) {
        fclose($handle);
        return ['message' => 'No headers found', 'type' => 'error', 'debug' => $debug];
    }
    
    $debug[] = "Headers found: " . implode(', ', $headers);
    
    // Clean headers
    $headers = array_map(function($header) {
        return trim(preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $header));
    }, $headers);
    
    $debug[] = "Cleaned headers: " . implode(', ', $headers);
    
    // Map headers
    $headerMap = [];
    foreach ($headers as $index => $header) {
        $headerMap[trim(strtolower($header))] = $index;
    }
    
    $debug[] = "Header mapping: " . json_encode($headerMap);
    
    // Check for required fields
    if (!isset($headerMap['name'])) {
        fclose($handle);
        return ['message' => 'Name column not found', 'type' => 'error', 'debug' => $debug];
    }
    
    // Count rows
    $rowCount = 0;
    $sampleRows = [];
    
    while (($row = fgetcsv($handle)) !== false && $rowCount < 5) {
        $rowCount++;
        $sampleRows[] = $row;
    }
    
    // Count total rows
    while (fgetcsv($handle) !== false) {
        $rowCount++;
    }
    
    fclose($handle);
    
    $debug[] = "Total data rows: $rowCount";
    $debug[] = "Sample rows: " . json_encode(array_slice($sampleRows, 0, 2));
    
    // Try to insert one sample record
    if (!empty($sampleRows)) {
        $sampleRow = $sampleRows[0];
        $studentData = [];
        foreach ($headerMap as $field => $index) {
            $value = isset($sampleRow[$index]) ? trim($sampleRow[$index]) : '';
            $studentData[$field] = $value;
        }
        
        $debug[] = "Sample student data: " . json_encode($studentData);
        
        // Try insert
        try {
            global $pdo;
            
            // Set defaults
            $studentData['session'] = $studentData['session'] ?? date('Y');
            $studentData['status'] = 'Active';
            $studentData['nationality'] = 'Bangladeshi';
            
            // Clean empty values
            if (empty($studentData['email'])) $studentData['email'] = null;
            if (empty($studentData['phone'])) $studentData['phone'] = null;
            if (empty($studentData['roll'])) $studentData['roll'] = null;
            
            $sql = "INSERT INTO students (roll, name, regi, `group`, fname, mname, gender, dob, session,
                    sub1, sub2, sub3, `4th_sub`, phone, email, address, blood_group, religion,
                    nationality, admission_date, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $params = [
                $studentData['roll'], $studentData['name'], $studentData['regi'] ?? null, $studentData['group'] ?? null,
                $studentData['fname'] ?? null, $studentData['mname'] ?? null, $studentData['gender'] ?? null,
                $studentData['dob'] ?? null, $studentData['session'], $studentData['sub1'] ?? null,
                $studentData['sub2'] ?? null, $studentData['sub3'] ?? null, $studentData['4th_sub'] ?? null,
                $studentData['phone'], $studentData['email'], $studentData['address'] ?? null,
                $studentData['blood_group'] ?? null, $studentData['religion'] ?? null, $studentData['nationality'],
                $studentData['admission_date'] ?? null, $studentData['status']
            ];
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);
            
            if ($result) {
                $debug[] = "✓ Sample insert successful";
                $insertedId = $pdo->lastInsertId();
                $debug[] = "Inserted ID: $insertedId";
                
                // Clean up test record
                $pdo->prepare("DELETE FROM students WHERE id = ?")->execute([$insertedId]);
                $debug[] = "Test record cleaned up";
            } else {
                $errorInfo = $stmt->errorInfo();
                $debug[] = "✗ Sample insert failed: " . $errorInfo[2];
            }
            
        } catch (Exception $e) {
            $debug[] = "✗ Insert exception: " . $e->getMessage();
        }
    }
    
    return [
        'message' => "Debug completed. Found $rowCount rows.",
        'type' => 'success',
        'debug' => $debug
    ];
}

$user = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Upload</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-bug me-2"></i>Debug Upload
                    </h1>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Upload Test</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="upload">
                                    
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">CSV File</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-2"></i>Debug Upload
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <?php if (!empty($debugInfo)): ?>
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Debug Information</h6>
                                </div>
                                <div class="card-body">
                                    <div style="max-height: 400px; overflow-y: auto;">
                                        <?php foreach ($debugInfo as $info): ?>
                                            <div class="mb-1">
                                                <small class="font-monospace"><?php echo htmlspecialchars($info); ?></small>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="bulk-upload.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Bulk Upload
                    </a>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
