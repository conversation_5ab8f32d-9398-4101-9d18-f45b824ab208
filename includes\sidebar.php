<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <h3>SKUL</h3>
        <p>শিক্ষা ব্যবস্থাপনা সিস্টেম</p>
    </div>
    
    <div class="sidebar-menu">
        <ul>
            <li>
                <a href="index.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>ড্যাশবোর্ড</span>
                </a>
            </li>
            
            <li>
                <a href="students/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/students/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-user-graduate"></i>
                    <span>ছাত্র-ছাত্রী</span>
                </a>
            </li>
            
            <li>
                <a href="teachers/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/teachers/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <span>শিক্ষক-শিক্ষিকা</span>
                </a>
            </li>
            
            <li>
                <a href="exams/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/exams/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-clipboard-list"></i>
                    <span>পরীক্ষা</span>
                </a>
            </li>
            
            <li>
                <a href="routines/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/routines/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-calendar-alt"></i>
                    <span>রুটিন</span>
                </a>
            </li>
            
            <li>
                <a href="fees/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/fees/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>ফি ব্যবস্থাপনা</span>
                </a>
            </li>
            
            <li>
                <a href="committees/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/committees/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i>
                    <span>কমিটি</span>
                </a>
            </li>
            
            <li>
                <a href="notices/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/notices/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-bullhorn"></i>
                    <span>নোটিশ</span>
                </a>
            </li>
            
            <li>
                <a href="reports/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/reports/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-chart-bar"></i>
                    <span>রিপোর্ট</span>
                </a>
            </li>
            
            <li>
                <a href="library/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/library/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-book"></i>
                    <span>লাইব্রেরি</span>
                </a>
            </li>
            
            <li>
                <a href="attendance/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/attendance/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-user-check"></i>
                    <span>উপস্থিতি</span>
                </a>
            </li>
            
            <li>
                <a href="transport/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/transport/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-bus"></i>
                    <span>পরিবহন</span>
                </a>
            </li>
        </ul>
        
        <hr style="border-color: rgba(255,255,255,0.2); margin: 20px 15px;">
        
        <ul>
            <li>
                <a href="settings/" class="<?php echo strpos($_SERVER['REQUEST_URI'], '/settings/') !== false ? 'active' : ''; ?>">
                    <i class="fas fa-cog"></i>
                    <span>সেটিংস</span>
                </a>
            </li>
            
            <li>
                <a href="profile.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'profile.php' ? 'active' : ''; ?>">
                    <i class="fas fa-user"></i>
                    <span>প্রোফাইল</span>
                </a>
            </li>
            
            <li>
                <a href="help.php" class="<?php echo basename($_SERVER['PHP_SELF']) == 'help.php' ? 'active' : ''; ?>">
                    <i class="fas fa-question-circle"></i>
                    <span>সাহায্য</span>
                </a>
            </li>
            
            <li>
                <a href="logout.php" onclick="return confirm('আপনি কি লগআউট করতে চান?');">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>লগআউট</span>
                </a>
            </li>
        </ul>
    </div>
</div>

<!-- Mobile Sidebar Overlay -->
<div class="sidebar-overlay d-md-none" id="sidebarOverlay"></div>

<script>
$(document).ready(function() {
    // Mobile sidebar toggle
    $('.sidebar-toggle').on('click', function() {
        $('#sidebar').toggleClass('show');
        $('#sidebarOverlay').toggleClass('show');
        $('body').toggleClass('sidebar-open');
    });
    
    // Close sidebar when clicking overlay
    $('#sidebarOverlay').on('click', function() {
        $('#sidebar').removeClass('show');
        $(this).removeClass('show');
        $('body').removeClass('sidebar-open');
    });
    
    // Close sidebar when clicking menu item on mobile
    $('.sidebar-menu a').on('click', function() {
        if ($(window).width() < 768) {
            $('#sidebar').removeClass('show');
            $('#sidebarOverlay').removeClass('show');
            $('body').removeClass('sidebar-open');
        }
    });
    
    // Handle window resize
    $(window).on('resize', function() {
        if ($(window).width() >= 768) {
            $('#sidebar').removeClass('show');
            $('#sidebarOverlay').removeClass('show');
            $('body').removeClass('sidebar-open');
        }
    });
});
</script>

<style>
/* Mobile Sidebar Styles */
@media (max-width: 767.98px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }
    
    body.sidebar-open {
        overflow: hidden;
    }
}

/* Sidebar Animation */
.sidebar-menu a {
    position: relative;
    overflow: hidden;
}

.sidebar-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.sidebar-menu a:hover::before {
    left: 100%;
}

/* Active Menu Item */
.sidebar-menu a.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sidebar-menu a.active::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: white;
    border-radius: 2px 0 0 2px;
}

/* Submenu Styles (for future use) */
.sidebar-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(0, 0, 0, 0.1);
    margin: 5px 15px;
    border-radius: 8px;
}

.sidebar-submenu.show {
    max-height: 300px;
}

.sidebar-submenu ul {
    padding: 10px 0;
}

.sidebar-submenu a {
    padding: 8px 20px 8px 40px;
    font-size: 14px;
}

/* Tooltip for collapsed sidebar (future feature) */
.sidebar.collapsed .sidebar-menu a {
    justify-content: center;
    padding: 12px;
}

.sidebar.collapsed .sidebar-menu span {
    display: none;
}

.sidebar.collapsed .sidebar-header {
    text-align: center;
    padding: 20px 10px;
}

.sidebar.collapsed .sidebar-header h3 {
    font-size: 1.2rem;
}

.sidebar.collapsed .sidebar-header p {
    display: none;
}
</style>
