<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->string('roll')->unique()->nullable();
            $table->string('name');
            $table->string('regi')->unique()->nullable();
            $table->string('group')->nullable();
            $table->string('fname')->nullable(); // Father's name
            $table->string('mname')->nullable(); // Mother's name
            $table->enum('gender', ['Male', 'Female', 'Other'])->nullable();
            $table->date('dob')->nullable(); // Date of birth
            $table->string('session')->nullable();
            $table->string('sub1')->nullable(); // Subject 1
            $table->string('sub2')->nullable(); // Subject 2
            $table->string('sub3')->nullable(); // Subject 3
            $table->string('4th_sub')->nullable(); // 4th Subject
            $table->string('picture')->nullable(); // Profile picture path
            
            // Additional fields for better management
            $table->string('phone')->nullable();
            $table->string('email')->unique()->nullable();
            $table->text('address')->nullable();
            $table->string('blood_group')->nullable();
            $table->string('religion')->nullable();
            $table->string('nationality')->default('Bangladeshi');
            $table->date('admission_date')->nullable();
            $table->string('previous_school')->nullable();
            $table->decimal('previous_gpa', 3, 2)->nullable();
            $table->enum('status', ['Active', 'Inactive', 'Graduated', 'Transferred'])->default('Active');
            $table->string('guardian_phone')->nullable();
            $table->string('guardian_email')->nullable();
            $table->text('guardian_address')->nullable();
            $table->string('emergency_contact')->nullable();
            $table->text('medical_info')->nullable();
            $table->text('notes')->nullable();
            
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['roll', 'regi', 'session', 'group', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};
