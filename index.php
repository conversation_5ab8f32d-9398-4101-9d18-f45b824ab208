<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if this is first time setup
if (!isset($_SESSION['first_visit_done'])) {
    $_SESSION['first_visit_done'] = true;
    header('Location: welcome.php');
    exit();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$user = getCurrentUser();
$stats = getDashboardStats();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SKUL - Education Management System</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Auto Setup Success Message -->
            <?php if (!isset($_SESSION['setup_message_shown'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>🎉 স্বাগতম!</strong> SKUL Education Management System সফলভাবে সেটআপ হয়েছে।
                    ডাটাবেস এবং সব টেবিল অটোমেটিক তৈরি হয়ে গেছে।
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php $_SESSION['setup_message_shown'] = true; ?>
            <?php endif; ?>
            <!-- Welcome Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="welcome-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="welcome-title">স্বাগতম, <?php echo htmlspecialchars($user['name']); ?>!</h2>
                                <p class="welcome-subtitle">আজকের শিক্ষা প্রতিষ্ঠানের সকল কার্যক্রম এক নজরে দেখুন</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <i class="fas fa-graduation-cap welcome-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="stats-card stats-primary">
                        <div class="stats-content">
                            <div class="stats-icon">
                                <i class="fas fa-user-graduate"></i>
                            </div>
                            <div class="stats-info">
                                <h3 class="stats-number" id="total-students"><?php echo number_format($stats['total_students']); ?></h3>
                                <p class="stats-label">মোট ছাত্র-ছাত্রী</p>
                                <small class="stats-detail">
                                    <span class="text-success"><?php echo $stats['active_students']; ?></span> সক্রিয়
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="stats-card stats-success">
                        <div class="stats-content">
                            <div class="stats-icon">
                                <i class="fas fa-chalkboard-teacher"></i>
                            </div>
                            <div class="stats-info">
                                <h3 class="stats-number" id="total-teachers"><?php echo number_format($stats['total_teachers']); ?></h3>
                                <p class="stats-label">মোট শিক্ষক</p>
                                <small class="stats-detail">
                                    <span class="text-success"><?php echo $stats['active_teachers']; ?></span> সক্রিয়
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="stats-card stats-warning">
                        <div class="stats-content">
                            <div class="stats-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stats-info">
                                <h3 class="stats-number" id="total-exams"><?php echo number_format($stats['total_exams']); ?></h3>
                                <p class="stats-label">মোট পরীক্ষা</p>
                                <small class="stats-detail">
                                    <span class="text-warning"><?php echo $stats['upcoming_exams']; ?></span> আসন্ন
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="stats-card stats-info">
                        <div class="stats-content">
                            <div class="stats-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stats-info">
                                <h3 class="stats-number" id="monthly-collection">৳<?php echo number_format($stats['monthly_collection']); ?></h3>
                                <p class="stats-label">এই মাসের ফি</p>
                                <small class="stats-detail">সংগৃহীত</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card chart-card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-line me-2"></i>
                                মাসিক ফি সংগ্রহ (<?php echo date('Y'); ?>)
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyFeeChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card chart-card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-pie me-2"></i>
                                গ্রুপ অনুযায়ী ছাত্র-ছাত্রী
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="studentsGroupChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activities -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card activity-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title">
                                <i class="fas fa-user-plus me-2"></i>
                                সাম্প্রতিক ছাত্র-ছাত্রী
                            </h5>
                            <a href="students/" class="btn btn-sm btn-outline-primary">সব দেখুন</a>
                        </div>
                        <div class="card-body">
                            <div id="recent-students-list">
                                <!-- Will be loaded via AJAX -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="card activity-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title">
                                <i class="fas fa-bullhorn me-2"></i>
                                সাম্প্রতিক নোটিশ
                            </h5>
                            <a href="notices/" class="btn btn-sm btn-outline-primary">সব দেখুন</a>
                        </div>
                        <div class="card-body">
                            <div id="recent-notices-list">
                                <!-- Will be loaded via AJAX -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-bolt me-2"></i>
                                দ্রুত কার্যক্রম
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="students/add.php" class="quick-action-btn">
                                        <i class="fas fa-user-plus"></i>
                                        <span>নতুন ছাত্র যোগ করুন</span>
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="teachers/add.php" class="quick-action-btn">
                                        <i class="fas fa-chalkboard-teacher"></i>
                                        <span>নতুন শিক্ষক যোগ করুন</span>
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="fees/collect.php" class="quick-action-btn">
                                        <i class="fas fa-money-bill"></i>
                                        <span>ফি সংগ্রহ করুন</span>
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="notices/add.php" class="quick-action-btn">
                                        <i class="fas fa-bullhorn"></i>
                                        <span>নোটিশ প্রকাশ করুন</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/charts.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // Initialize dashboard
        $(document).ready(function() {
            loadRecentStudents();
            loadRecentNotices();
            initializeCharts();
            
            // Auto-refresh stats every 30 seconds
            setInterval(function() {
                refreshStats();
            }, 30000);
        });
        
        // Load recent students via AJAX
        function loadRecentStudents() {
            $.ajax({
                url: 'api/recent-students.php',
                method: 'GET',
                dataType: 'json',
                success: function(data) {
                    let html = '';
                    if (data.length > 0) {
                        data.forEach(function(student) {
                            html += `
                                <div class="activity-item">
                                    <div class="activity-avatar">
                                        <img src="${student.picture || 'assets/images/default-avatar.png'}" alt="${student.name}">
                                    </div>
                                    <div class="activity-content">
                                        <h6 class="activity-title">${student.name}</h6>
                                        <p class="activity-desc">${student.roll} • ${student.group || 'N/A'}</p>
                                        <small class="activity-time">${student.created_at}</small>
                                    </div>
                                    <div class="activity-status">
                                        <span class="badge bg-success">সক্রিয়</span>
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        html = '<p class="text-muted text-center">কোনো সাম্প্রতিক ছাত্র-ছাত্রী নেই</p>';
                    }
                    $('#recent-students-list').html(html);
                },
                error: function() {
                    $('#recent-students-list').html('<p class="text-danger text-center">ডেটা লোড করতে সমস্যা হয়েছে</p>');
                }
            });
        }
        
        // Load recent notices via AJAX
        function loadRecentNotices() {
            $.ajax({
                url: 'api/recent-notices.php',
                method: 'GET',
                dataType: 'json',
                success: function(data) {
                    let html = '';
                    if (data.length > 0) {
                        data.forEach(function(notice) {
                            html += `
                                <div class="activity-item">
                                    <div class="activity-content">
                                        <h6 class="activity-title">${notice.title}</h6>
                                        <p class="activity-desc">${notice.content.substring(0, 100)}...</p>
                                        <small class="activity-time">${notice.publish_date}</small>
                                    </div>
                                    <div class="activity-status">
                                        <span class="badge bg-${notice.priority_class}">${notice.priority}</span>
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        html = '<p class="text-muted text-center">কোনো সাম্প্রতিক নোটিশ নেই</p>';
                    }
                    $('#recent-notices-list').html(html);
                },
                error: function() {
                    $('#recent-notices-list').html('<p class="text-danger text-center">ডেটা লোড করতে সমস্যা হয়েছে</p>');
                }
            });
        }
        
        // Refresh statistics
        function refreshStats() {
            $.ajax({
                url: 'api/dashboard-stats.php',
                method: 'GET',
                dataType: 'json',
                success: function(data) {
                    $('#total-students').text(data.total_students.toLocaleString());
                    $('#total-teachers').text(data.total_teachers.toLocaleString());
                    $('#total-exams').text(data.total_exams.toLocaleString());
                    $('#monthly-collection').text('৳' + data.monthly_collection.toLocaleString());
                }
            });
        }
    </script>
</body>
</html>
