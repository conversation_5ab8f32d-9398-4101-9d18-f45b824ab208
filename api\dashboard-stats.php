<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    errorResponse('Unauthorized', 401);
}

try {
    $stats = getDashboardStats();
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($stats);
    
} catch (Exception $e) {
    errorResponse('Error fetching dashboard stats: ' . $e->getMessage());
}
?>
