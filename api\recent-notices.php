<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    errorResponse('Unauthorized', 401);
}

try {
    // Get recent notices (last 5)
    $sql = "SELECT n.id, n.title, n.content, n.type, n.priority, n.publish_date, 
                   t.name as creator_name
            FROM notices n
            LEFT JOIN teachers t ON n.created_by = t.id
            WHERE n.status = 'Published'
            ORDER BY n.publish_date DESC 
            LIMIT 5";
    
    $notices = fetchAll($sql);
    
    // Format the data
    $formattedNotices = [];
    foreach ($notices as $notice) {
        $priorityClass = [
            'Low' => 'info',
            'Medium' => 'warning',
            'High' => 'danger',
            'Urgent' => 'danger'
        ];
        
        $formattedNotices[] = [
            'id' => $notice['id'],
            'title' => $notice['title'],
            'content' => $notice['content'],
            'type' => $notice['type'],
            'priority' => $notice['priority'],
            'priority_class' => $priorityClass[$notice['priority']] ?? 'info',
            'publish_date' => formatDate($notice['publish_date'], 'd M Y'),
            'creator_name' => $notice['creator_name']
        ];
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($formattedNotices);
    
} catch (Exception $e) {
    errorResponse('Error fetching recent notices: ' . $e->getMessage());
}
?>
