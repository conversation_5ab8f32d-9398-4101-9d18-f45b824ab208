<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - SKUL Education System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 50px auto;
            max-width: 800px;
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 40px;
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .step.success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .step.error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .step.processing {
            border-color: #ffc107;
            background-color: #fff3cd;
        }
        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
        }
        .btn-setup:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <h1><i class="fas fa-database me-2"></i>SKUL Database Setup</h1>
                <p class="mb-0">শিক্ষা ব্যবস্থাপনা সিস্টেমের ডাটাবেস সেটআপ</p>
            </div>
            
            <div class="setup-body">
                <?php
                $setupComplete = false;
                $errors = [];
                $logs = [];
                
                if (isset($_POST['setup_database'])) {
                    try {
                        // Database configuration
                        $host = $_POST['db_host'] ?? 'localhost';
                        $username = $_POST['db_username'] ?? 'root';
                        $password = $_POST['db_password'] ?? '';
                        $dbname = $_POST['db_name'] ?? 'skul_education_db';
                        
                        $logs[] = "ডাটাবেস সংযোগ শুরু করা হচ্ছে...";
                        
                        // Connect without database first
                        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        $logs[] = "✓ MySQL সার্ভারে সংযোগ সফল";
                        
                        // Create database
                        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                        $logs[] = "✓ ডাটাবেস '$dbname' তৈরি/যাচাই সম্পন্ন";
                        
                        // Connect to the specific database
                        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        $logs[] = "✓ ডাটাবেসে সংযোগ সফল";
                        
                        // Create tables
                        $tables = [
                            'users' => "CREATE TABLE IF NOT EXISTS users (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                name VARCHAR(255) NOT NULL,
                                email VARCHAR(255) UNIQUE NOT NULL,
                                password VARCHAR(255) NOT NULL,
                                role ENUM('admin', 'teacher', 'student', 'staff') DEFAULT 'student',
                                user_type VARCHAR(50) NULL,
                                user_id BIGINT NULL,
                                is_active BOOLEAN DEFAULT TRUE,
                                last_login TIMESTAMP NULL,
                                avatar VARCHAR(255) NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                INDEX idx_role_active (role, is_active)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
                            
                            'students' => "CREATE TABLE IF NOT EXISTS students (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                roll VARCHAR(50) UNIQUE NULL,
                                name VARCHAR(255) NOT NULL,
                                regi VARCHAR(50) UNIQUE NULL,
                                `group` VARCHAR(100) NULL,
                                fname VARCHAR(255) NULL COMMENT 'Father name',
                                mname VARCHAR(255) NULL COMMENT 'Mother name',
                                gender ENUM('Male', 'Female', 'Other') NULL,
                                dob DATE NULL COMMENT 'Date of birth',
                                session VARCHAR(20) NULL,
                                sub1 VARCHAR(100) NULL COMMENT 'Subject 1',
                                sub2 VARCHAR(100) NULL COMMENT 'Subject 2',
                                sub3 VARCHAR(100) NULL COMMENT 'Subject 3',
                                4th_sub VARCHAR(100) NULL COMMENT '4th Subject',
                                picture VARCHAR(255) NULL,
                                phone VARCHAR(20) NULL,
                                email VARCHAR(255) UNIQUE NULL,
                                address TEXT NULL,
                                blood_group VARCHAR(10) NULL,
                                religion VARCHAR(50) NULL,
                                nationality VARCHAR(50) DEFAULT 'Bangladeshi',
                                admission_date DATE NULL,
                                previous_school VARCHAR(255) NULL,
                                previous_gpa DECIMAL(3,2) NULL,
                                status ENUM('Active', 'Inactive', 'Graduated', 'Transferred') DEFAULT 'Active',
                                guardian_phone VARCHAR(20) NULL,
                                guardian_email VARCHAR(255) NULL,
                                guardian_address TEXT NULL,
                                emergency_contact VARCHAR(20) NULL,
                                medical_info TEXT NULL,
                                notes TEXT NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                INDEX idx_roll_regi_session (roll, regi, session),
                                INDEX idx_group_status (`group`, status)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
                            
                            'teachers' => "CREATE TABLE IF NOT EXISTS teachers (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                employee_id VARCHAR(50) UNIQUE NOT NULL,
                                name VARCHAR(255) NOT NULL,
                                email VARCHAR(255) UNIQUE NULL,
                                phone VARCHAR(20) NULL,
                                gender ENUM('Male', 'Female', 'Other') NOT NULL,
                                dob DATE NULL,
                                address TEXT NULL,
                                designation VARCHAR(100) NOT NULL,
                                department VARCHAR(100) NOT NULL,
                                qualification VARCHAR(255) NULL,
                                specialization VARCHAR(255) NULL,
                                joining_date DATE NOT NULL,
                                salary DECIMAL(10,2) NULL,
                                blood_group VARCHAR(10) NULL,
                                religion VARCHAR(50) NULL,
                                nationality VARCHAR(50) DEFAULT 'Bangladeshi',
                                nid VARCHAR(20) UNIQUE NULL,
                                picture VARCHAR(255) NULL,
                                status ENUM('Active', 'Inactive', 'Resigned', 'Retired') DEFAULT 'Active',
                                emergency_contact VARCHAR(20) NULL,
                                experience TEXT NULL,
                                notes TEXT NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                INDEX idx_employee_dept_status (employee_id, department, status)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
                            
                            'notices' => "CREATE TABLE IF NOT EXISTS notices (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                title VARCHAR(255) NOT NULL,
                                content TEXT NOT NULL,
                                type VARCHAR(100) NOT NULL,
                                priority VARCHAR(50) NOT NULL,
                                publish_date DATE NOT NULL,
                                expiry_date DATE NULL,
                                target_audience VARCHAR(100) NULL,
                                attachment VARCHAR(255) NULL,
                                created_by INT NOT NULL,
                                is_published BOOLEAN DEFAULT FALSE,
                                send_notification BOOLEAN DEFAULT FALSE,
                                status ENUM('Draft', 'Published', 'Expired', 'Archived') DEFAULT 'Draft',
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                INDEX idx_type_priority_status (type, priority, status)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
                        ];
                        
                        foreach ($tables as $tableName => $sql) {
                            $pdo->exec($sql);
                            $logs[] = "✓ টেবিল '$tableName' তৈরি সম্পন্ন";
                        }
                        
                        // Insert default admin user
                        $adminExists = $pdo->query("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'")->fetchColumn();
                        
                        if ($adminExists == 0) {
                            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, is_active) VALUES (?, ?, ?, ?, ?)");
                            $stmt->execute(['System Administrator', '<EMAIL>', $hashedPassword, 'admin', 1]);
                            $logs[] = "✓ ডিফল্ট অ্যাডমিন ইউজার তৈরি সম্পন্ন";
                        } else {
                            $logs[] = "✓ অ্যাডমিন ইউজার ইতিমধ্যে বিদ্যমান";
                        }
                        
                        // Insert sample data
                        $sampleStudents = [
                            ['STU24001', 'মোহাম্মদ রহিম', 'REG2024001', 'Science', 'আব্দুল করিম', 'ফাতেমা খাতুন', 'Male', '2005-01-15', '2024'],
                            ['STU24002', 'ফাতেমা আক্তার', 'REG2024002', 'Commerce', 'মোহাম্মদ আলী', 'রোকেয়া বেগম', 'Female', '2005-03-20', '2024'],
                            ['STU24003', 'আব্দুল্লাহ আল মামুন', 'REG2024003', 'Arts', 'মোহাম্মদ হাসান', 'সালমা খাতুন', 'Male', '2005-05-10', '2024']
                        ];
                        
                        $studentCount = $pdo->query("SELECT COUNT(*) FROM students")->fetchColumn();
                        if ($studentCount == 0) {
                            $stmt = $pdo->prepare("INSERT INTO students (roll, name, regi, `group`, fname, mname, gender, dob, session, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'Active')");
                            
                            foreach ($sampleStudents as $student) {
                                $stmt->execute($student);
                            }
                            $logs[] = "✓ নমুনা ছাত্র ডেটা যোগ সম্পন্ন";
                        }
                        
                        // Update config file
                        $configContent = "<?php
// Database configuration
define('DB_HOST', '$host');
define('DB_USERNAME', '$username');
define('DB_PASSWORD', '$password');
define('DB_NAME', '$dbname');

// Create connection
try {
    \$pdo = new PDO(\"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\", DB_USERNAME, DB_PASSWORD);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException \$e) {
    die(\"Connection failed: \" . \$e->getMessage());
}

// Function to execute queries
function executeQuery(\$sql, \$params = []) {
    global \$pdo;
    try {
        \$stmt = \$pdo->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt;
    } catch(PDOException \$e) {
        error_log(\"Database error: \" . \$e->getMessage());
        return false;
    }
}

// Function to fetch single row
function fetchRow(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    return \$stmt ? \$stmt->fetch() : false;
}

// Function to fetch all rows
function fetchAll(\$sql, \$params = []) {
    \$stmt = executeQuery(\$sql, \$params);
    return \$stmt ? \$stmt->fetchAll() : false;
}

// Function to get last insert ID
function getLastInsertId() {
    global \$pdo;
    return \$pdo->lastInsertId();
}
?>";
                        
                        file_put_contents('config/database.php', $configContent);
                        $logs[] = "✓ কনফিগারেশন ফাইল আপডেট সম্পন্ন";
                        
                        $setupComplete = true;
                        $logs[] = "🎉 ডাটাবেস সেটআপ সফলভাবে সম্পন্ন!";
                        
                    } catch (Exception $e) {
                        $errors[] = "❌ Error: " . $e->getMessage();
                        $logs[] = "❌ সেটআপে সমস্যা: " . $e->getMessage();
                    }
                }
                ?>
                
                <?php if (!$setupComplete && empty($_POST)): ?>
                    <div class="step">
                        <h4><i class="fas fa-info-circle text-primary me-2"></i>ডাটাবেস সেটআপ</h4>
                        <p>SKUL Education Management System ব্যবহার করার জন্য প্রথমে ডাটাবেস সেটআপ করুন।</p>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="db_host" class="form-label">Database Host</label>
                                        <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="db_name" class="form-label">Database Name</label>
                                        <input type="text" class="form-control" id="db_name" name="db_name" value="skul_education_db" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="db_username" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="db_username" name="db_username" value="root" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="db_password" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="db_password" name="db_password" placeholder="(খালি রাখুন XAMPP এর জন্য)">
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" name="setup_database" class="btn btn-setup">
                                <i class="fas fa-play me-2"></i>ডাটাবেস সেটআপ শুরু করুন
                            </button>
                        </form>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($logs)): ?>
                    <div class="step <?php echo $setupComplete ? 'success' : (!empty($errors) ? 'error' : 'processing'); ?>">
                        <h4><i class="fas fa-terminal text-info me-2"></i>সেটআপ লগ</h4>
                        <div class="log-output">
                            <?php foreach ($logs as $log): ?>
                                <div><?php echo htmlspecialchars($log); ?></div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($setupComplete): ?>
                    <div class="step success">
                        <h4><i class="fas fa-check-circle text-success me-2"></i>সেটআপ সম্পন্ন!</h4>
                        <p>ডাটাবেস সফলভাবে তৈরি হয়েছে। এখন আপনি সিস্টেম ব্যবহার করতে পারেন।</p>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-key me-2"></i>ডিফল্ট লগইন তথ্য:</h6>
                            <strong>Email:</strong> <EMAIL><br>
                            <strong>Password:</strong> admin123
                        </div>
                        
                        <div class="d-flex gap-2">
                            <a href="index.php" class="btn btn-setup">
                                <i class="fas fa-home me-2"></i>ড্যাশবোর্ডে যান
                            </a>
                            <a href="login.php" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>লগইন করুন
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                    <div class="step error">
                        <h4><i class="fas fa-exclamation-triangle text-danger me-2"></i>সমস্যা</h4>
                        <?php foreach ($errors as $error): ?>
                            <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                        <?php endforeach; ?>
                        
                        <p><strong>সমাধান:</strong></p>
                        <ul>
                            <li>XAMPP Control Panel থেকে MySQL সার্ভিস চালু আছে কিনা নিশ্চিত করুন</li>
                            <li>Database credentials সঠিক আছে কিনা যাচাই করুন</li>
                            <li>MySQL এর default port (3306) ব্যবহার হচ্ছে কিনা দেখুন</li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
