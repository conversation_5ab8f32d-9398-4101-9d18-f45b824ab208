<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    errorResponse('Unauthorized', 401);
}

try {
    // Get recent students (last 5)
    $sql = "SELECT id, roll, name, regi, `group`, picture, status, created_at 
            FROM students 
            ORDER BY created_at DESC 
            LIMIT 5";
    
    $students = fetchAll($sql);
    
    // Format the data
    $formattedStudents = [];
    foreach ($students as $student) {
        $formattedStudents[] = [
            'id' => $student['id'],
            'roll' => $student['roll'],
            'name' => $student['name'],
            'regi' => $student['regi'],
            'group' => $student['group'],
            'picture' => $student['picture'] ? 'uploads/students/' . $student['picture'] : null,
            'status' => $student['status'],
            'created_at' => formatDate($student['created_at'], 'd M Y')
        ];
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($formattedStudents);
    
} catch (Exception $e) {
    errorResponse('Error fetching recent students: ' . $e->getMessage());
}
?>
