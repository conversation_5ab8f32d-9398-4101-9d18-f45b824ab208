<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Committee extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'description',
        'formation_date',
        'expiry_date',
        'status'
    ];

    protected $casts = [
        'formation_date' => 'date',
        'expiry_date' => 'date'
    ];

    // Relationships
    public function members()
    {
        return $this->hasMany(CommitteeMember::class);
    }

    public function activeMembers()
    {
        return $this->hasMany(CommitteeMember::class)->where('status', 'Active');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Helper methods
    public function getChairman()
    {
        return $this->members()
                   ->where('position', 'Chairman')
                   ->where('status', 'Active')
                   ->with('teacher')
                   ->first();
    }

    public function getSecretary()
    {
        return $this->members()
                   ->where('position', 'Secretary')
                   ->where('status', 'Active')
                   ->with('teacher')
                   ->first();
    }

    public function getMemberCount()
    {
        return $this->activeMembers()->count();
    }

    public function isExpired()
    {
        return $this->expiry_date && $this->expiry_date < now()->toDateString();
    }

    public function dissolve()
    {
        $this->update(['status' => 'Dissolved']);
        $this->members()->update(['status' => 'Inactive']);
    }
}
