<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create principal user
        User::create([
            'name' => 'Principal',
            'email' => '<EMAIL>',
            'password' => Hash::make('principal123'),
            'role' => 'admin',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create demo teacher user
        User::create([
            'name' => 'Demo Teacher',
            'email' => '<EMAIL>',
            'password' => Hash::make('teacher123'),
            'role' => 'teacher',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create demo student user
        User::create([
            'name' => 'Demo Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('student123'),
            'role' => 'student',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
    }
}
