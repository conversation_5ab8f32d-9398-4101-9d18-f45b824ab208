<?php
// Authentication functions
function login($email, $password) {
    $user = fetchRow("SELECT * FROM users WHERE email = ? AND is_active = 1", [$email]);
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_email'] = $user['email'];
        
        // Update last login
        executeQuery("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
        
        return true;
    }
    
    return false;
}

function logout() {
    session_destroy();
    header('Location: login.php');
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

function hasRole($role) {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
}

function requireRole($role) {
    requireLogin();
    if (!hasRole($role)) {
        header('HTTP/1.0 403 Forbidden');
        die('Access denied');
    }
}

// User functions
function getUserById($id) {
    return fetchRow("SELECT * FROM users WHERE id = ?", [$id]);
}

function getCurrentUser() {
    if (isset($_SESSION['user_id'])) {
        try {
            return getUserById($_SESSION['user_id']);
        } catch (Exception $e) {
            error_log("Error getting current user: " . $e->getMessage());
            return null;
        }
    }
    return null;
}

// Safe user data getter with fallback
function getSafeUserData() {
    $user = getCurrentUser();
    if (!$user) {
        return [
            'id' => 0,
            'name' => 'Guest User',
            'email' => '',
            'role' => 'guest',
            'avatar' => null,
            'is_active' => false
        ];
    }
    return $user;
}

function createUser($data) {
    $sql = "INSERT INTO users (name, email, password, role, user_type, user_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)";
    $params = [
        $data['name'],
        $data['email'],
        password_hash($data['password'], PASSWORD_DEFAULT),
        $data['role'],
        $data['user_type'] ?? null,
        $data['user_id'] ?? null,
        $data['is_active'] ?? 1
    ];
    
    return executeQuery($sql, $params);
}

// Student functions
function getStudents($filters = []) {
    $sql = "SELECT * FROM students WHERE 1=1";
    $params = [];
    
    if (!empty($filters['search'])) {
        $sql .= " AND (name LIKE ? OR roll LIKE ? OR regi LIKE ? OR phone LIKE ? OR email LIKE ?)";
        $searchTerm = '%' . $filters['search'] . '%';
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    if (!empty($filters['session'])) {
        $sql .= " AND session = ?";
        $params[] = $filters['session'];
    }
    
    if (!empty($filters['group'])) {
        $sql .= " AND `group` = ?";
        $params[] = $filters['group'];
    }
    
    if (!empty($filters['status'])) {
        $sql .= " AND status = ?";
        $params[] = $filters['status'];
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    if (!empty($filters['limit'])) {
        $sql .= " LIMIT " . intval($filters['limit']);
    }
    
    return fetchAll($sql, $params);
}

function getStudentById($id) {
    return fetchRow("SELECT * FROM students WHERE id = ?", [$id]);
}

function createStudent($data) {
    // Generate roll and registration if not provided
    if (empty($data['roll'])) {
        $data['roll'] = generateRoll();
    }
    
    if (empty($data['regi'])) {
        $data['regi'] = generateRegistration();
    }
    
    $sql = "INSERT INTO students (roll, name, regi, `group`, fname, mname, gender, dob, session, sub1, sub2, sub3, `4th_sub`, picture, phone, email, address, blood_group, religion, nationality, admission_date, previous_school, previous_gpa, status, guardian_phone, guardian_email, guardian_address, emergency_contact, medical_info, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $data['roll'], $data['name'], $data['regi'], $data['group'], $data['fname'],
        $data['mname'], $data['gender'], $data['dob'], $data['session'], $data['sub1'],
        $data['sub2'], $data['sub3'], $data['4th_sub'], $data['picture'], $data['phone'],
        $data['email'], $data['address'], $data['blood_group'], $data['religion'],
        $data['nationality'], $data['admission_date'], $data['previous_school'],
        $data['previous_gpa'], $data['status'], $data['guardian_phone'],
        $data['guardian_email'], $data['guardian_address'], $data['emergency_contact'],
        $data['medical_info'], $data['notes']
    ];
    
    return executeQuery($sql, $params);
}

function updateStudent($id, $data) {
    $sql = "UPDATE students SET roll=?, name=?, regi=?, `group`=?, fname=?, mname=?, gender=?, dob=?, session=?, sub1=?, sub2=?, sub3=?, `4th_sub`=?, picture=?, phone=?, email=?, address=?, blood_group=?, religion=?, nationality=?, admission_date=?, previous_school=?, previous_gpa=?, status=?, guardian_phone=?, guardian_email=?, guardian_address=?, emergency_contact=?, medical_info=?, notes=?, updated_at=NOW() WHERE id=?";
    
    $params = [
        $data['roll'], $data['name'], $data['regi'], $data['group'], $data['fname'],
        $data['mname'], $data['gender'], $data['dob'], $data['session'], $data['sub1'],
        $data['sub2'], $data['sub3'], $data['4th_sub'], $data['picture'], $data['phone'],
        $data['email'], $data['address'], $data['blood_group'], $data['religion'],
        $data['nationality'], $data['admission_date'], $data['previous_school'],
        $data['previous_gpa'], $data['status'], $data['guardian_phone'],
        $data['guardian_email'], $data['guardian_address'], $data['emergency_contact'],
        $data['medical_info'], $data['notes'], $id
    ];
    
    return executeQuery($sql, $params);
}

function deleteStudent($id) {
    try {
        // Get student details for cleanup
        $student = fetchRow("SELECT * FROM students WHERE id = ?", [$id]);

        if (!$student) {
            return false;
        }

        // Start transaction
        executeQuery("START TRANSACTION");

        // Delete related data first
        executeQuery("DELETE FROM fee_payments WHERE student_id = ?", [$id]);
        executeQuery("DELETE FROM exam_results WHERE student_id = ?", [$id]);

        // Delete student picture if exists
        if (!empty($student['picture'])) {
            $picturePath = 'uploads/students/' . $student['picture'];
            if (file_exists($picturePath)) {
                unlink($picturePath);
            }
        }

        // Delete student record
        $result = executeQuery("DELETE FROM students WHERE id = ?", [$id]);

        // Commit transaction
        executeQuery("COMMIT");

        return $result;

    } catch (Exception $e) {
        // Rollback on error
        executeQuery("ROLLBACK");
        return false;
    }
}

// Generate functions
function generateRoll() {
    $year = date('y');
    $prefix = 'STU' . $year;
    
    $lastStudent = fetchRow("SELECT roll FROM students WHERE roll LIKE ? ORDER BY roll DESC LIMIT 1", [$prefix . '%']);
    
    if ($lastStudent) {
        $lastNumber = intval(substr($lastStudent['roll'], -4));
        $newNumber = $lastNumber + 1;
    } else {
        $newNumber = 1;
    }
    
    return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
}

function generateRegistration() {
    $year = date('Y');
    $prefix = 'REG' . $year;
    
    $lastStudent = fetchRow("SELECT regi FROM students WHERE regi LIKE ? ORDER BY regi DESC LIMIT 1", [$prefix . '%']);
    
    if ($lastStudent) {
        $lastNumber = intval(substr($lastStudent['regi'], -6));
        $newNumber = $lastNumber + 1;
    } else {
        $newNumber = 1;
    }
    
    return $prefix . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
}

// Dashboard functions
function getDashboardStats() {
    $stats = [];
    
    // Student statistics
    $stats['total_students'] = fetchRow("SELECT COUNT(*) as count FROM students")['count'];
    $stats['active_students'] = fetchRow("SELECT COUNT(*) as count FROM students WHERE status = 'Active'")['count'];
    
    // Teacher statistics
    $stats['total_teachers'] = fetchRow("SELECT COUNT(*) as count FROM teachers")['count'];
    $stats['active_teachers'] = fetchRow("SELECT COUNT(*) as count FROM teachers WHERE status = 'Active'")['count'];
    
    // Exam statistics
    $stats['total_exams'] = fetchRow("SELECT COUNT(*) as count FROM exams")['count'];
    $stats['upcoming_exams'] = fetchRow("SELECT COUNT(*) as count FROM exams WHERE start_date > CURDATE()")['count'];
    
    // Fee collection statistics
    $currentMonth = date('Y-m');
    $stats['monthly_collection'] = fetchRow("SELECT COALESCE(SUM(amount_paid), 0) as total FROM fee_payments WHERE DATE_FORMAT(payment_date, '%Y-%m') = ? AND status = 'Paid'", [$currentMonth])['total'];
    
    return $stats;
}

function getMonthlyFeeCollection($year) {
    $sql = "SELECT MONTH(payment_date) as month, SUM(amount_paid) as total 
            FROM fee_payments 
            WHERE YEAR(payment_date) = ? AND status = 'Paid' 
            GROUP BY MONTH(payment_date) 
            ORDER BY month";
    
    $results = fetchAll($sql, [$year]);
    
    // Fill missing months with 0
    $monthlyData = array_fill(1, 12, 0);
    foreach ($results as $row) {
        $monthlyData[$row['month']] = floatval($row['total']);
    }
    
    return array_values($monthlyData);
}

function getStudentsByGroup() {
    $sql = "SELECT `group`, COUNT(*) as total FROM students WHERE status = 'Active' AND `group` IS NOT NULL GROUP BY `group`";
    $results = fetchAll($sql);
    
    $data = [];
    foreach ($results as $row) {
        $data[$row['group']] = intval($row['total']);
    }
    
    return $data;
}

// Utility functions
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function formatDate($date, $format = 'd/m/Y') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

function formatCurrency($amount) {
    return '৳' . number_format($amount, 2);
}

function uploadFile($file, $uploadDir = 'uploads/') {
    if (!isset($file['error']) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }
    
    $maxSize = 2 * 1024 * 1024; // 2MB
    if ($file['size'] > $maxSize) {
        return false;
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $uploadPath = $uploadDir . $filename;
    
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        return $filename;
    }
    
    return false;
}

function deleteFile($filename, $uploadDir = 'uploads/') {
    $filePath = $uploadDir . $filename;
    if (file_exists($filePath)) {
        return unlink($filePath);
    }
    return false;
}

// Response functions
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

function successResponse($message, $data = null) {
    jsonResponse([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

function errorResponse($message, $status = 400) {
    jsonResponse([
        'success' => false,
        'message' => $message
    ], $status);
}

// CSV processing functions
function validateCSVHeaders($headers, $requiredHeaders = ['name']) {
    $headerMap = [];
    foreach ($headers as $index => $header) {
        $headerMap[trim(strtolower($header))] = $index;
    }

    $missingHeaders = [];
    foreach ($requiredHeaders as $required) {
        if (!isset($headerMap[$required])) {
            $missingHeaders[] = $required;
        }
    }

    return [
        'valid' => empty($missingHeaders),
        'missing' => $missingHeaders,
        'map' => $headerMap
    ];
}

function parseCSVRow($row, $headerMap) {
    $data = [];
    foreach ($headerMap as $field => $index) {
        $value = isset($row[$index]) ? trim($row[$index]) : '';
        $data[$field] = $value;
    }
    return $data;
}

function validateStudentData($data, $rowNumber) {
    $errors = [];

    // Required field validation
    if (empty($data['name'])) {
        $errors[] = "সারি $rowNumber: ছাত্রের নাম প্রয়োজন।";
    }

    // Email validation
    if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = "সারি $rowNumber: ইমেইল ঠিকানা সঠিক নয়।";
    }

    // Date validation
    if (!empty($data['dob'])) {
        $dob = strtotime($data['dob']);
        if (!$dob) {
            $errors[] = "সারি $rowNumber: জন্ম তারিখ সঠিক নয়।";
        }
    }

    // Blood group validation
    $validBloodGroups = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    if (!empty($data['blood_group']) && !in_array($data['blood_group'], $validBloodGroups)) {
        $errors[] = "সারি $rowNumber: রক্তের গ্রুপ সঠিক নয়।";
    }

    // Gender validation
    $validGenders = ['Male', 'Female', 'Other'];
    if (!empty($data['gender']) && !in_array($data['gender'], $validGenders)) {
        $errors[] = "সারি $rowNumber: লিঙ্গ সঠিক নয়।";
    }

    return $errors;
}

function checkDuplicateStudent($data) {
    $duplicates = [];

    // Check registration number only (roll can be duplicate across different groups)
    if (!empty($data['regi'])) {
        $existing = fetchRow("SELECT id FROM students WHERE regi = ?", [$data['regi']]);
        if ($existing) {
            $duplicates[] = "রেজিস্ট্রেশন নম্বর '{$data['regi']}' ইতিমধ্যে বিদ্যমান।";
        }
    }

    // Check email
    if (!empty($data['email'])) {
        $existing = fetchRow("SELECT id FROM students WHERE email = ?", [$data['email']]);
        if ($existing) {
            $duplicates[] = "ইমেইল '{$data['email']}' ইতিমধ্যে বিদ্যমান।";
        }
    }

    return $duplicates;
}
?>
