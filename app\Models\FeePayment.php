<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FeePayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'fee_id',
        'amount_paid',
        'payment_date',
        'payment_method',
        'receipt_number',
        'transaction_id',
        'remarks',
        'collected_by',
        'status'
    ];

    protected $casts = [
        'amount_paid' => 'decimal:2',
        'payment_date' => 'date'
    ];

    // Relationships
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function fee()
    {
        return $this->belongsTo(Fee::class);
    }

    public function collector()
    {
        return $this->belongsTo(Teacher::class, 'collected_by');
    }

    // Scopes
    public function scopePaid($query)
    {
        return $query->where('status', 'Paid');
    }

    public function scopeByMonth($query, $month, $year = null)
    {
        $query->whereMonth('payment_date', $month);
        
        if ($year) {
            $query->whereYear('payment_date', $year);
        }
        
        return $query;
    }

    public function scopeByYear($query, $year)
    {
        return $query->whereYear('payment_date', $year);
    }

    // Helper methods
    public function generateReceiptNumber()
    {
        $year = date('Y');
        $month = date('m');
        
        $lastPayment = static::where('receipt_number', 'like', "RCP{$year}{$month}%")
                           ->orderBy('receipt_number', 'desc')
                           ->first();
        
        if ($lastPayment) {
            $lastNumber = (int) substr($lastPayment->receipt_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return "RCP{$year}{$month}" . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    public function isFullPayment()
    {
        return $this->amount_paid >= $this->fee->amount;
    }

    public function getRemainingAmount()
    {
        return $this->fee->amount - $this->amount_paid;
    }
}
