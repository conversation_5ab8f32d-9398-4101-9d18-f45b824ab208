<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('committees', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // Academic, Disciplinary, Sports, etc.
            $table->text('description')->nullable();
            $table->date('formation_date');
            $table->date('expiry_date')->nullable();
            $table->enum('status', ['Active', 'Inactive', 'Dissolved'])->default('Active');
            $table->timestamps();
            
            $table->index(['type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('committees');
    }
};
