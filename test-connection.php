<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test - SKUL</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-body {
            padding: 30px;
        }
        .test-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .test-success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .test-error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .test-info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-database me-2"></i>Database Connection Test</h1>
            <p class="mb-0">SKUL Education Management System</p>
        </div>
        
        <div class="test-body">
            <?php
            echo "<div class='test-item test-info'>";
            echo "<h5><i class='fas fa-info-circle me-2'></i>Testing Database Connection...</h5>";
            echo "</div>";
            
            try {
                // Include database configuration
                require_once 'config/database.php';
                
                echo "<div class='test-item test-success'>";
                echo "<h5><i class='fas fa-check-circle me-2'></i>✅ Database Connection Successful!</h5>";
                echo "<p>Successfully connected to database: <strong>" . DB_NAME . "</strong></p>";
                echo "</div>";
                
                // Test tables
                $tables = ['users', 'students', 'teachers', 'exams', 'fees', 'fee_payments', 'notices'];
                
                echo "<div class='test-item test-info'>";
                echo "<h5><i class='fas fa-table me-2'></i>Checking Tables...</h5>";
                echo "</div>";
                
                foreach ($tables as $table) {
                    try {
                        $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
                        echo "<div class='test-item test-success'>";
                        echo "<p><i class='fas fa-check me-2'></i>Table <strong>$table</strong>: $count records</p>";
                        echo "</div>";
                    } catch (Exception $e) {
                        echo "<div class='test-item test-error'>";
                        echo "<p><i class='fas fa-times me-2'></i>Table <strong>$table</strong>: Error - " . $e->getMessage() . "</p>";
                        echo "</div>";
                    }
                }
                
                // Test admin user
                echo "<div class='test-item test-info'>";
                echo "<h5><i class='fas fa-user-shield me-2'></i>Checking Admin User...</h5>";
                echo "</div>";
                
                $admin = $pdo->query("SELECT * FROM users WHERE email = '<EMAIL>'")->fetch();
                if ($admin) {
                    echo "<div class='test-item test-success'>";
                    echo "<p><i class='fas fa-check me-2'></i>Admin user found: <strong>" . $admin['name'] . "</strong></p>";
                    echo "<p><strong>Email:</strong> " . $admin['email'] . "</p>";
                    echo "<p><strong>Role:</strong> " . $admin['role'] . "</p>";
                    echo "<p><strong>Status:</strong> " . ($admin['is_active'] ? 'Active' : 'Inactive') . "</p>";
                    echo "</div>";
                } else {
                    echo "<div class='test-item test-error'>";
                    echo "<p><i class='fas fa-times me-2'></i>Admin user not found!</p>";
                    echo "</div>";
                }
                
                // Test sample data
                echo "<div class='test-item test-info'>";
                echo "<h5><i class='fas fa-users me-2'></i>Sample Data Status...</h5>";
                echo "</div>";
                
                $studentCount = $pdo->query("SELECT COUNT(*) FROM students")->fetchColumn();
                $noticeCount = $pdo->query("SELECT COUNT(*) FROM notices")->fetchColumn();
                
                echo "<div class='test-item test-success'>";
                echo "<p><i class='fas fa-user-graduate me-2'></i>Sample Students: <strong>$studentCount</strong></p>";
                echo "<p><i class='fas fa-bullhorn me-2'></i>Sample Notices: <strong>$noticeCount</strong></p>";
                echo "</div>";
                
                // Show sample students
                if ($studentCount > 0) {
                    echo "<div class='test-item test-info'>";
                    echo "<h6><i class='fas fa-list me-2'></i>Sample Students:</h6>";
                    $students = $pdo->query("SELECT name, roll, `group` FROM students LIMIT 5")->fetchAll();
                    echo "<ul>";
                    foreach ($students as $student) {
                        echo "<li>{$student['name']} (Roll: {$student['roll']}, Group: {$student['group']})</li>";
                    }
                    echo "</ul>";
                    echo "</div>";
                }
                
                echo "<div class='test-item test-success'>";
                echo "<h5><i class='fas fa-thumbs-up me-2'></i>🎉 All Tests Passed!</h5>";
                echo "<p>Your SKUL Education Management System is ready to use.</p>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='test-item test-error'>";
                echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>❌ Connection Failed!</h5>";
                echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
                echo "<p><strong>Solution:</strong></p>";
                echo "<ul>";
                echo "<li>Make sure XAMPP is running</li>";
                echo "<li>Check if MySQL service is started</li>";
                echo "<li>Verify database credentials in config/database.php</li>";
                echo "</ul>";
                echo "</div>";
            }
            ?>
            
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-primary me-2">
                    <i class="fas fa-home me-1"></i>Go to Dashboard
                </a>
                <a href="login.php" class="btn btn-success me-2">
                    <i class="fas fa-sign-in-alt me-1"></i>Login
                </a>
                <a href="welcome.php" class="btn btn-info">
                    <i class="fas fa-star me-1"></i>Welcome Page
                </a>
            </div>
            
            <div class="mt-4 pt-3 border-top">
                <h6>Quick Info:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Database Host:</strong> <?php echo DB_HOST; ?></p>
                        <p><strong>Database Name:</strong> <?php echo DB_NAME; ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Admin Email:</strong> <EMAIL></p>
                        <p><strong>Admin Password:</strong> admin123</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
