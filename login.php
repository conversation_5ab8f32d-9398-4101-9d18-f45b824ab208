<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $error = 'ইমেইল এবং পাসওয়ার্ড প্রয়োজন';
    } elseif (!validateEmail($email)) {
        $error = 'বৈধ ইমেইল ঠিকানা দিন';
    } else {
        if (login($email, $password)) {
            // Set remember me cookie if checked
            if ($remember) {
                setcookie('remember_token', session_id(), time() + (30 * 24 * 60 * 60), '/'); // 30 days
            }
            
            header('Location: index.php');
            exit();
        } else {
            $error = 'ভুল ইমেইল বা পাসওয়ার্ড';
        }
    }
}

// Handle logout message
if (isset($_GET['logout'])) {
    $success = 'সফলভাবে লগআউট হয়েছে';
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>লগইন - SKUL Education Management System</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        
        .login-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .login-logo {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .login-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .login-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
        }
        
        .demo-credentials {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .demo-credentials h6 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .demo-credentials .credential-item {
            background: white;
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-credentials .credential-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-credentials .credential-item:last-child {
            margin-bottom: 0;
        }
        
        .demo-credentials small {
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            .login-left {
                padding: 40px 20px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .login-title {
                font-size: 2rem;
            }
            
            .login-logo {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0">
            <!-- Left Side -->
            <div class="col-lg-6 login-left">
                <div>
                    <i class="fas fa-graduation-cap login-logo"></i>
                    <h1 class="login-title">SKUL</h1>
                    <p class="login-subtitle">শিক্ষা ব্যবস্থাপনা সিস্টেম</p>
                    <p class="mb-0">
                        আধুনিক প্রযুক্তির সাহায্যে আপনার শিক্ষা প্রতিষ্ঠানের সকল কার্যক্রম 
                        সহজ এবং কার্যকরভাবে পরিচালনা করুন।
                    </p>
                </div>
            </div>
            
            <!-- Right Side -->
            <div class="col-lg-6 login-right">
                <div class="text-center mb-4">
                    <h2 class="h3 mb-2">স্বাগতম!</h2>
                    <p class="text-muted">আপনার অ্যাকাউন্টে প্রবেশ করুন</p>
                </div>
                
                <!-- Alerts -->
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Login Form -->
                <form method="POST" action="login.php" id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">ইমেইল ঠিকানা</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-envelope text-muted"></i>
                            </span>
                            <input type="email" class="form-control border-start-0" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                   required autofocus placeholder="আপনার ইমেইল দিন">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">পাসওয়ার্ড</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-lock text-muted"></i>
                            </span>
                            <input type="password" class="form-control border-start-0" id="password" name="password" 
                                   required placeholder="আপনার পাসওয়ার্ড দিন">
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3 d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                মনে রাখুন
                            </label>
                        </div>
                        <a href="forgot-password.php" class="text-decoration-none">
                            পাসওয়ার্ড ভুলে গেছেন?
                        </a>
                    </div>
                    
                    <button type="submit" class="btn btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        প্রবেশ করুন
                    </button>
                </form>
                
                <!-- Demo Credentials -->
                <div class="demo-credentials">
                    <h6><i class="fas fa-info-circle me-2"></i>ডেমো অ্যাকাউন্ট</h6>
                    
                    <div class="credential-item" data-email="<EMAIL>" data-password="admin123">
                        <strong>অ্যাডমিন:</strong> <EMAIL>
                        <br><small>পাসওয়ার্ড: admin123</small>
                    </div>
                    
                    <div class="credential-item" data-email="<EMAIL>" data-password="teacher123">
                        <strong>শিক্ষক:</strong> <EMAIL>
                        <br><small>পাসওয়ার্ড: teacher123</small>
                    </div>
                    
                    <div class="credential-item" data-email="<EMAIL>" data-password="student123">
                        <strong>ছাত্র:</strong> <EMAIL>
                        <br><small>পাসওয়ার্ড: student123</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
            
            // Toggle password visibility
            $('#togglePassword').on('click', function() {
                const passwordField = $('#password');
                const icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });
            
            // Demo credential click to fill
            $('.credential-item').on('click', function() {
                const email = $(this).data('email');
                const password = $(this).data('password');
                
                $('#email').val(email);
                $('#password').val(password);
                
                // Add visual feedback
                $(this).addClass('border-success');
                setTimeout(() => {
                    $(this).removeClass('border-success');
                }, 1000);
            });
            
            // Form validation
            $('#loginForm').on('submit', function(e) {
                const email = $('#email').val().trim();
                const password = $('#password').val();
                
                if (!email || !password) {
                    e.preventDefault();
                    alert('ইমেইল এবং পাসওয়ার্ড প্রয়োজন');
                    return false;
                }
                
                if (!isValidEmail(email)) {
                    e.preventDefault();
                    alert('বৈধ ইমেইল ঠিকানা দিন');
                    return false;
                }
                
                // Show loading
                $(this).find('button[type="submit"]').html('<i class="fas fa-spinner fa-spin me-2"></i>প্রবেশ করা হচ্ছে...');
            });
            
            // Email validation function
            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }
        });
    </script>
</body>
</html>
