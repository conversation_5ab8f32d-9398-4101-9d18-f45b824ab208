<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h1>Debug Information</h1>";

echo "<h3>Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>User Data:</h3>";
$user = getCurrentUser();
echo "<pre>";
print_r($user);
echo "</pre>";

echo "<h3>Safe User Data:</h3>";
$safeUser = getSafeUserData();
echo "<pre>";
print_r($safeUser);
echo "</pre>";

echo "<h3>Database Connection:</h3>";
try {
    $count = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "✅ Database connected. Users count: $count<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>Functions Available:</h3>";
echo "✅ getCurrentUser(): " . (function_exists('getCurrentUser') ? 'Yes' : 'No') . "<br>";
echo "✅ getSafeUserData(): " . (function_exists('getSafeUserData') ? 'Yes' : 'No') . "<br>";
echo "✅ getUserById(): " . (function_exists('getUserById') ? 'Yes' : 'No') . "<br>";

echo "<br><a href='index.php'>Go to Dashboard</a> | <a href='login.php'>Login</a>";
?>
