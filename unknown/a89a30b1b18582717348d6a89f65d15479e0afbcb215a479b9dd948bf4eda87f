<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('routines', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // Class Routine, Exam Routine
            $table->string('session');
            $table->string('group')->nullable();
            $table->string('day'); // Saturday, Sunday, Monday, etc.
            $table->string('period'); // 1st Period, 2nd Period, etc.
            $table->time('start_time');
            $table->time('end_time');
            $table->string('subject')->nullable();
            $table->foreignId('teacher_id')->nullable()->constrained()->onDelete('set null');
            $table->string('room')->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['Active', 'Inactive'])->default('Active');
            $table->timestamps();
            
            $table->index(['type', 'session', 'group', 'day', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('routines');
    }
};
