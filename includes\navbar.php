<?php
// Get user data if logged in (if not already set)
if (!isset($user)) {
    $user = getSafeUserData();
}
// Ensure user is not null
if (!$user) {
    $user = getSafeUserData();
}
?>

<nav class="navbar navbar-expand-lg">
    <div class="container-fluid">
        <!-- Mobile Sidebar Toggle -->
        <button class="btn btn-outline-primary d-md-none sidebar-toggle me-3" type="button">
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- Page Title -->
        <div class="navbar-brand d-flex align-items-center">
            <i class="fas fa-graduation-cap me-2 text-primary"></i>
            <span class="fw-bold">SKUL Education System</span>
        </div>
        
        <!-- Right Side Items -->
        <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
            <!-- Notifications -->
            <div class="nav-item dropdown me-3">
                <a class="nav-link position-relative" href="#" id="notificationDropdown" role="button" 
                   data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-bell fs-5"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" 
                          id="notificationCount" style="font-size: 0.7rem;">
                        3
                    </span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end notification-dropdown" 
                    aria-labelledby="notificationDropdown">
                    <li class="dropdown-header d-flex justify-content-between align-items-center">
                        <span>নোটিফিকেশন</span>
                        <a href="#" class="text-primary small" onclick="markAllAsRead()">সব পড়া হয়েছে</a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    
                    <li>
                        <a class="dropdown-item notification-item unread" href="notices/view.php?id=1">
                            <div class="d-flex">
                                <div class="notification-icon bg-primary">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <div class="notification-content">
                                    <h6 class="notification-title">নতুন নোটিশ প্রকাশিত</h6>
                                    <p class="notification-text">আগামীকাল ছুটির দিন ঘোষণা</p>
                                    <small class="notification-time">২ মিনিট আগে</small>
                                </div>
                            </div>
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item notification-item unread" href="students/view.php?id=123">
                            <div class="d-flex">
                                <div class="notification-icon bg-success">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="notification-content">
                                    <h6 class="notification-title">নতুন ছাত্র ভর্তি</h6>
                                    <p class="notification-text">মোহাম্মদ রহিম ভর্তি হয়েছে</p>
                                    <small class="notification-time">১৫ মিনিট আগে</small>
                                </div>
                            </div>
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item notification-item" href="fees/payments.php">
                            <div class="d-flex">
                                <div class="notification-icon bg-warning">
                                    <i class="fas fa-money-bill"></i>
                                </div>
                                <div class="notification-content">
                                    <h6 class="notification-title">ফি পেমেন্ট</h6>
                                    <p class="notification-text">৫টি নতুন ফি পেমেন্ট</p>
                                    <small class="notification-time">১ ঘন্টা আগে</small>
                                </div>
                            </div>
                        </a>
                    </li>
                    
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-center text-primary" href="notifications.php">
                            সব নোটিফিকেশন দেখুন
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Quick Search -->
            <div class="nav-item me-3">
                <div class="search-container">
                    <input type="text" class="form-control search-input" placeholder="অনুসন্ধান..." 
                           id="quickSearch" autocomplete="off">
                    <i class="fas fa-search search-icon"></i>
                    <div class="search-results" id="searchResults"></div>
                </div>
            </div>
            
            <!-- User Profile -->
            <?php if ($user && $user['role'] !== 'guest'): ?>
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown"
                   role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <img src="<?php echo $user['avatar'] ?? 'assets/images/default-avatar.svg'; ?>"
                         alt="Profile" class="rounded-circle me-2" width="32" height="32">
                    <span class="d-none d-sm-inline"><?php echo htmlspecialchars($user['name']); ?></span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end user-dropdown" aria-labelledby="userDropdown">
                    <li class="dropdown-header">
                        <div class="text-center">
                            <img src="<?php echo $user['avatar'] ?? 'assets/images/default-avatar.svg'; ?>"
                                 alt="Profile" class="rounded-circle mb-2" width="60" height="60">
                            <h6 class="mb-0"><?php echo htmlspecialchars($user['name']); ?></h6>
                            <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                            <br>
                            <span class="badge bg-primary mt-1"><?php echo ucfirst($user['role']); ?></span>
                        </div>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    
                    <li>
                        <a class="dropdown-item" href="profile.php">
                            <i class="fas fa-user me-2"></i>
                            প্রোফাইল
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item" href="settings/">
                            <i class="fas fa-cog me-2"></i>
                            সেটিংস
                        </a>
                    </li>
                    
                    <li>
                        <a class="dropdown-item" href="help.php">
                            <i class="fas fa-question-circle me-2"></i>
                            সাহায্য
                        </a>
                    </li>
                    
                    <li><hr class="dropdown-divider"></li>
                    
                    <li>
                        <a class="dropdown-item text-danger" href="logout.php" 
                           onclick="return confirm('আপনি কি লগআউট করতে চান?');">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            <?php else: ?>
            <!-- Login Button for non-logged in users -->
            <div class="nav-item">
                <a href="login.php" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-1"></i>লগইন
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</nav>

<style>
/* Navbar Styles */
.navbar {
    background: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    margin-bottom: 20px;
    padding: 15px 25px;
}

/* Search Container */
.search-container {
    position: relative;
    width: 300px;
}

.search-input {
    padding-left: 40px;
    border-radius: 25px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    width: 350px;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-results.show {
    display: block;
}

.search-result-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Notification Dropdown */
.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #f0f8ff;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
    font-size: 14px;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #2c3e50;
}

.notification-text {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 4px;
}

.notification-time {
    font-size: 12px;
    color: #adb5bd;
}

/* User Dropdown */
.user-dropdown {
    width: 280px;
}

.user-dropdown .dropdown-header {
    padding: 20px 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: -8px -15px 0 -15px;
    border-radius: 8px 8px 0 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .search-container {
        width: 200px;
    }
    
    .search-input:focus {
        width: 220px;
    }
    
    .notification-dropdown {
        width: 300px;
    }
    
    .user-dropdown {
        width: 250px;
    }
}

@media (max-width: 576px) {
    .navbar {
        padding: 10px 15px;
    }
    
    .search-container {
        width: 150px;
    }
    
    .search-input:focus {
        width: 180px;
    }
}
</style>

<script>
$(document).ready(function() {
    // Quick search functionality
    let searchTimeout;
    
    $('#quickSearch').on('input', function() {
        const searchTerm = $(this).val().trim();
        
        clearTimeout(searchTimeout);
        
        if (searchTerm.length >= 2) {
            searchTimeout = setTimeout(function() {
                performQuickSearch(searchTerm);
            }, 300);
        } else {
            $('#searchResults').removeClass('show').empty();
        }
    });
    
    // Hide search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.search-container').length) {
            $('#searchResults').removeClass('show');
        }
    });
    
    // Load notifications count
    loadNotificationCount();
    
    // Auto-refresh notifications every 30 seconds
    setInterval(loadNotificationCount, 30000);
});

function performQuickSearch(term) {
    $.ajax({
        url: 'api/quick-search.php',
        method: 'GET',
        data: { term: term },
        dataType: 'json',
        success: function(data) {
            displaySearchResults(data);
        },
        error: function() {
            $('#searchResults').removeClass('show').empty();
        }
    });
}

function displaySearchResults(results) {
    const container = $('#searchResults');
    let html = '';
    
    if (results.length > 0) {
        results.forEach(function(item) {
            html += `
                <div class="search-result-item" onclick="window.location.href='${item.url}'">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="${item.icon} text-primary"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">${item.title}</h6>
                            <small class="text-muted">${item.description}</small>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        html = '<div class="search-result-item text-center text-muted">কোনো ফলাফল পাওয়া যায়নি</div>';
    }
    
    container.html(html).addClass('show');
}

function loadNotificationCount() {
    $.ajax({
        url: 'api/notification-count.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            const count = data.count || 0;
            const badge = $('#notificationCount');
            
            if (count > 0) {
                badge.text(count > 99 ? '99+' : count).show();
            } else {
                badge.hide();
            }
        }
    });
}

function markAllAsRead() {
    $.ajax({
        url: 'api/mark-notifications-read.php',
        method: 'POST',
        dataType: 'json',
        success: function(data) {
            if (data.success) {
                $('.notification-item.unread').removeClass('unread');
                $('#notificationCount').hide();
                SKUL.showNotification('সব নোটিফিকেশন পড়া হয়েছে হিসেবে চিহ্নিত', 'success');
            }
        }
    });
}
</script>
