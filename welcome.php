<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>স্বাগতম - SKUL Education Management System</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .welcome-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
            margin: 20px;
            overflow: hidden;
        }
        
        .welcome-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .welcome-body {
            padding: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="welcome-header">
            <i class="fas fa-graduation-cap fa-4x mb-3"></i>
            <h1 class="mb-3">🎉 স্বাগতম SKUL এ!</h1>
            <p class="mb-0 fs-5">আধুনিক শিক্ষা ব্যবস্থাপনা সিস্টেম</p>
        </div>
        
        <div class="welcome-body">
            <div class="text-center mb-4">
                <h3 class="text-success">✅ সিস্টেম সফলভাবে সেটআপ হয়েছে!</h3>
                <p class="text-muted">সব কিছু প্রস্তুত। এখন আপনি সিস্টেম ব্যবহার করতে পারেন।</p>
            </div>
            
            <!-- System Stats -->
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">5</div>
                    <div class="stat-label">নমুনা ছাত্র-ছাত্রী</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">নমুনা নোটিশ</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">7</div>
                    <div class="stat-label">ডাটাবেস টেবিল</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1</div>
                    <div class="stat-label">অ্যাডমিন ইউজার</div>
                </div>
            </div>
            
            <!-- Features -->
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-user-graduate text-primary me-2"></i>ছাত্র ব্যবস্থাপনা</h5>
                        <p class="mb-0">সম্পূর্ণ ছাত্র তথ্য, ছবি আপলোড, অনুসন্ধান এবং ফিল্টার</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-chart-line text-success me-2"></i>ইন্টারঅ্যাক্টিভ ড্যাশবোর্ড</h5>
                        <p class="mb-0">রিয়েল-টাইম পরিসংখ্যান এবং সুন্দর চার্ট</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-mobile-alt text-warning me-2"></i>রেসপন্সিভ ডিজাইন</h5>
                        <p class="mb-0">সব ডিভাইসে (মোবাইল, ট্যাবলেট, ডেস্কটপ) কাজ করে</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="feature-card">
                        <h5><i class="fas fa-shield-alt text-danger me-2"></i>নিরাপদ সিস্টেম</h5>
                        <p class="mb-0">আধুনিক নিরাপত্তা ব্যবস্থা এবং ডেটা সুরক্ষা</p>
                    </div>
                </div>
            </div>
            
            <!-- Login Info -->
            <div class="alert alert-info mt-4">
                <h6><i class="fas fa-key me-2"></i>লগইন তথ্য:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Email:</strong> <EMAIL>
                    </div>
                    <div class="col-md-6">
                        <strong>Password:</strong> admin123
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-tachometer-alt me-2"></i>ড্যাশবোর্ডে যান
                </a>
                <a href="login.php" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>লগইন করুন
                </a>
            </div>
            
            <!-- Quick Links -->
            <div class="mt-4 pt-4 border-top">
                <h6 class="text-center mb-3">দ্রুত লিংক:</h6>
                <div class="row text-center">
                    <div class="col-md-3">
                        <a href="students/" class="text-decoration-none">
                            <i class="fas fa-user-graduate fa-2x text-primary d-block mb-2"></i>
                            <small>ছাত্র-ছাত্রী</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="teachers/" class="text-decoration-none">
                            <i class="fas fa-chalkboard-teacher fa-2x text-success d-block mb-2"></i>
                            <small>শিক্ষক-শিক্ষিকা</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="notices/" class="text-decoration-none">
                            <i class="fas fa-bullhorn fa-2x text-warning d-block mb-2"></i>
                            <small>নোটিশ বোর্ড</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="reports/" class="text-decoration-none">
                            <i class="fas fa-chart-bar fa-2x text-info d-block mb-2"></i>
                            <small>রিপোর্ট</small>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="text-center mt-4 pt-4 border-top">
                <p class="text-muted mb-0">
                    <i class="fas fa-heart text-danger"></i> 
                    SKUL Education Management System দিয়ে তৈরি
                </p>
                <small class="text-muted">Version 1.0 | PHP + MySQL + JavaScript + AJAX</small>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto redirect to dashboard after 10 seconds
        setTimeout(function() {
            if (confirm('ড্যাশবোর্ডে যেতে চান?')) {
                window.location.href = 'index.php';
            }
        }, 10000);
        
        // Add some animation
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
