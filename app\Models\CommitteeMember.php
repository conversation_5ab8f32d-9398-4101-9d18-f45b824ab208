<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CommitteeMember extends Model
{
    use HasFactory;

    protected $fillable = [
        'committee_id',
        'teacher_id',
        'position',
        'join_date',
        'leave_date',
        'status'
    ];

    protected $casts = [
        'join_date' => 'date',
        'leave_date' => 'date'
    ];

    // Relationships
    public function committee()
    {
        return $this->belongsTo(Committee::class);
    }

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    // Helper methods
    public function getTenure()
    {
        $endDate = $this->leave_date ?? now();
        return $this->join_date->diffInDays($endDate);
    }

    public function resign()
    {
        $this->update([
            'status' => 'Inactive',
            'leave_date' => now()->toDateString()
        ]);
    }
}
