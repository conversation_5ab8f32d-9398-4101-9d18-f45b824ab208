<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$message = '';
$messageType = '';

// Handle index removal
if ($_POST['remove_indexes'] ?? false) {
    try {
        global $pdo;
        
        $results = [];
        
        // Get all indexes for students table
        $stmt = $pdo->query("SHOW INDEX FROM students");
        $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $indexesToRemove = [];
        foreach ($indexes as $index) {
            $keyName = $index['Key_name'];
            $columnName = $index['Column_name'];
            
            // Skip PRIMARY key
            if ($keyName === 'PRIMARY') {
                continue;
            }
            
            // Collect unique indexes for email, phone, roll
            if (in_array($columnName, ['email', 'phone', 'roll']) && $index['Non_unique'] == 0) {
                $indexesToRemove[] = $keyName;
            }
        }
        
        // Remove duplicate index names
        $indexesToRemove = array_unique($indexesToRemove);
        
        // Drop each index
        foreach ($indexesToRemove as $indexName) {
            try {
                $pdo->exec("ALTER TABLE students DROP INDEX `$indexName`");
                $results[] = "✓ Removed index: $indexName";
            } catch (Exception $e) {
                $results[] = "⚠ Failed to remove $indexName: " . $e->getMessage();
            }
        }
        
        if (empty($indexesToRemove)) {
            $results[] = "ℹ No UNIQUE indexes found for email, phone, or roll";
        }
        
        // Clean up empty values
        $stmt = $pdo->prepare("UPDATE students SET email = NULL WHERE email = ''");
        $stmt->execute();
        $emailUpdated = $stmt->rowCount();
        $results[] = "✓ Updated $emailUpdated empty email records to NULL";
        
        $stmt = $pdo->prepare("UPDATE students SET phone = NULL WHERE phone = ''");
        $stmt->execute();
        $phoneUpdated = $stmt->rowCount();
        $results[] = "✓ Updated $phoneUpdated empty phone records to NULL";
        
        $stmt = $pdo->prepare("UPDATE students SET roll = NULL WHERE roll = ''");
        $stmt->execute();
        $rollUpdated = $stmt->rowCount();
        $results[] = "✓ Updated $rollUpdated empty roll records to NULL";
        
        // Remove duplicate registrations
        $duplicateQuery = "
            DELETE s1 FROM students s1
            INNER JOIN students s2 
            WHERE s1.id > s2.id 
            AND s1.regi = s2.regi 
            AND s1.regi IS NOT NULL 
            AND s1.regi != ''
        ";
        $stmt = $pdo->prepare($duplicateQuery);
        $stmt->execute();
        $duplicatesRemoved = $stmt->rowCount();
        $results[] = "✓ Removed $duplicatesRemoved duplicate registration records";
        
        $message = implode("\n", $results);
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Get current indexes
try {
    global $pdo;
    $stmt = $pdo->query("SHOW INDEX FROM students");
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $indexes = [];
    $indexError = $e->getMessage();
}

// Count issues
try {
    $emptyEmails = fetchRow("SELECT COUNT(*) as count FROM students WHERE email = ''")['count'];
    $emptyPhones = fetchRow("SELECT COUNT(*) as count FROM students WHERE phone = ''")['count'];
    $emptyRolls = fetchRow("SELECT COUNT(*) as count FROM students WHERE roll = ''")['count'];
    $duplicateRegis = fetchRow("SELECT COUNT(*) - COUNT(DISTINCT regi) as count FROM students WHERE regi IS NOT NULL AND regi != ''")['count'];
    $totalStudents = fetchRow("SELECT COUNT(*) as count FROM students")['count'];
} catch (Exception $e) {
    $emptyEmails = $emptyPhones = $emptyRolls = $duplicateRegis = $totalStudents = 'Error';
}

$user = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check & Fix Indexes</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-search me-2"></i>Check & Fix Database Indexes
                    </h1>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                        <pre style="white-space: pre-wrap; margin: 0;"><?php echo htmlspecialchars($message); ?></pre>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-database me-2"></i>Current Database Status
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6>Data Summary:</h6>
                                                <ul class="mb-0">
                                                    <li>Total Students: <?php echo $totalStudents; ?></li>
                                                    <li>Empty Emails: <?php echo $emptyEmails; ?></li>
                                                    <li>Empty Phones: <?php echo $emptyPhones; ?></li>
                                                    <li>Empty Rolls: <?php echo $emptyRolls; ?></li>
                                                    <li>Duplicate Regis: <?php echo $duplicateRegis; ?></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6>Current Indexes:</h6>
                                                <div style="max-height: 200px; overflow-y: auto;">
                                                    <?php if (isset($indexError)): ?>
                                                        <div class="text-danger">Error: <?php echo htmlspecialchars($indexError); ?></div>
                                                    <?php else: ?>
                                                        <table class="table table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>Key Name</th>
                                                                    <th>Column</th>
                                                                    <th>Unique</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php foreach ($indexes as $index): ?>
                                                                <tr class="<?php echo $index['Non_unique'] == 0 && in_array($index['Column_name'], ['email', 'phone', 'roll']) ? 'table-warning' : ''; ?>">
                                                                    <td><?php echo htmlspecialchars($index['Key_name']); ?></td>
                                                                    <td><?php echo htmlspecialchars($index['Column_name']); ?></td>
                                                                    <td><?php echo $index['Non_unique'] == 0 ? '✓' : ''; ?></td>
                                                                </tr>
                                                                <?php endforeach; ?>
                                                            </tbody>
                                                        </table>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <h6 class="text-info mb-2">
                                        <i class="fas fa-info-circle me-2"></i>What This Will Do:
                                    </h6>
                                    <ul class="mb-0">
                                        <li>Automatically detect and remove UNIQUE indexes on email, phone, roll</li>
                                        <li>Convert empty strings ('') to NULL values</li>
                                        <li>Remove duplicate registration records</li>
                                        <li>Allow successful CSV bulk upload</li>
                                    </ul>
                                </div>

                                <form method="POST" onsubmit="return confirm('Are you sure you want to fix the database indexes and data?');">
                                    <button type="submit" name="remove_indexes" value="1" class="btn btn-danger btn-lg">
                                        <i class="fas fa-tools me-2"></i>Fix All Database Issues
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-list-check me-2"></i>Fix Actions
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Auto-detect problematic indexes</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Remove UNIQUE constraints</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Clean empty values</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Remove duplicates</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Enable bulk upload</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <a href="unique-regi-upload.php" class="btn btn-outline-primary">
                                <i class="fas fa-upload me-2"></i>Back to Upload
                            </a>
                            <a href="index.php" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-list me-2"></i>Students List
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
