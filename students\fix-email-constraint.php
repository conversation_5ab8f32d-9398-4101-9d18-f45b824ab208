<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$message = '';
$messageType = '';

// Handle constraint fix
if ($_POST['fix_constraint'] ?? false) {
    try {
        global $pdo;
        
        // Check current constraints
        $stmt = $pdo->query("SHOW CREATE TABLE students");
        $tableInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        $createTable = $tableInfo['Create Table'];
        
        $results = [];
        
        // Remove UNIQUE constraint from email if exists
        if (strpos($createTable, 'UNIQUE KEY') !== false && strpos($createTable, 'email') !== false) {
            try {
                $pdo->exec("ALTER TABLE students DROP INDEX email");
                $results[] = "✓ Email UNIQUE constraint removed";
            } catch (Exception $e) {
                $results[] = "⚠ Email constraint removal: " . $e->getMessage();
            }
        } else {
            $results[] = "ℹ No email UNIQUE constraint found";
        }
        
        // Remove UNIQUE constraint from phone if exists
        if (strpos($createTable, 'phone') !== false) {
            try {
                $pdo->exec("ALTER TABLE students DROP INDEX phone");
                $results[] = "✓ Phone UNIQUE constraint removed";
            } catch (Exception $e) {
                $results[] = "⚠ Phone constraint removal: " . $e->getMessage();
            }
        }

        // Remove UNIQUE constraint from roll if exists
        if (strpos($createTable, 'roll') !== false && strpos($createTable, 'UNIQUE') !== false) {
            try {
                $pdo->exec("ALTER TABLE students DROP INDEX roll");
                $results[] = "✓ Roll UNIQUE constraint removed";
            } catch (Exception $e) {
                $results[] = "⚠ Roll constraint removal: " . $e->getMessage();
            }
        }
        
        // Clean up existing empty email records
        $stmt = $pdo->prepare("UPDATE students SET email = NULL WHERE email = ''");
        $stmt->execute();
        $emailUpdated = $stmt->rowCount();
        $results[] = "✓ Updated $emailUpdated empty email records to NULL";
        
        // Clean up existing empty phone records
        $stmt = $pdo->prepare("UPDATE students SET phone = NULL WHERE phone = ''");
        $stmt->execute();
        $phoneUpdated = $stmt->rowCount();
        $results[] = "✓ Updated $phoneUpdated empty phone records to NULL";

        // Clean up existing empty roll records
        $stmt = $pdo->prepare("UPDATE students SET roll = NULL WHERE roll = ''");
        $stmt->execute();
        $rollUpdated = $stmt->rowCount();
        $results[] = "✓ Updated $rollUpdated empty roll records to NULL";

        // Remove duplicate registration numbers (keep the first one)
        $duplicateQuery = "
            DELETE s1 FROM students s1
            INNER JOIN students s2
            WHERE s1.id > s2.id
            AND s1.regi = s2.regi
            AND s1.regi IS NOT NULL
            AND s1.regi != ''
        ";
        $stmt = $pdo->prepare($duplicateQuery);
        $stmt->execute();
        $duplicatesRemoved = $stmt->rowCount();
        $results[] = "✓ Removed $duplicatesRemoved duplicate registration records";
        
        $message = implode("\n", $results);
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Check current table structure
try {
    global $pdo;
    $stmt = $pdo->query("SHOW CREATE TABLE students");
    $tableInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    $createTable = $tableInfo['Create Table'];
    
    // Check for constraints
    $hasEmailUnique = strpos($createTable, 'UNIQUE KEY') !== false && strpos($createTable, 'email') !== false;
    $hasPhoneUnique = strpos($createTable, 'phone') !== false && strpos($createTable, 'UNIQUE') !== false;
    $hasRollUnique = strpos($createTable, 'roll') !== false && strpos($createTable, 'UNIQUE') !== false;
    
} catch (Exception $e) {
    $createTable = 'Error: ' . $e->getMessage();
    $hasEmailUnique = false;
    $hasPhoneUnique = false;
    $hasRollUnique = false;
}

// Count empty email/phone/roll records and duplicates
try {
    $emptyEmails = fetchRow("SELECT COUNT(*) as count FROM students WHERE email = ''")['count'];
    $emptyPhones = fetchRow("SELECT COUNT(*) as count FROM students WHERE phone = ''")['count'];
    $emptyRolls = fetchRow("SELECT COUNT(*) as count FROM students WHERE roll = ''")['count'];
    $duplicateRegis = fetchRow("SELECT COUNT(*) - COUNT(DISTINCT regi) as count FROM students WHERE regi IS NOT NULL AND regi != ''")['count'];
} catch (Exception $e) {
    $emptyEmails = 'Error';
    $emptyPhones = 'Error';
    $emptyRolls = 'Error';
    $duplicateRegis = 'Error';
}

$user = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Email Constraint</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-wrench me-2"></i>Fix Email Constraint
                    </h1>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                        <pre style="white-space: pre-wrap; margin: 0;"><?php echo htmlspecialchars($message); ?></pre>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-database me-2"></i>Database Constraint Issues
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <h6 class="text-warning mb-2">
                                        <i class="fas fa-exclamation-triangle me-2"></i>সমস্যা:
                                    </h6>
                                    <ul class="mb-2">
                                        <li>Email, Phone, Roll fields এ UNIQUE constraint আছে</li>
                                        <li>CSV এ অনেক empty values আছে যা duplicate হিসেবে count হচ্ছে</li>
                                        <li>Duplicate registration numbers আছে</li>
                                    </ul>
                                    <p class="mb-0"><strong>সমাধান:</strong> UNIQUE constraints remove করে empty values NULL করতে হবে এবং duplicate registrations remove করতে হবে।</p>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6>Current Status:</h6>
                                                <ul class="mb-0">
                                                    <li>Email UNIQUE: <?php echo $hasEmailUnique ? '❌ Yes (Problem)' : '✅ No (Good)'; ?></li>
                                                    <li>Phone UNIQUE: <?php echo $hasPhoneUnique ? '❌ Yes (Problem)' : '✅ No (Good)'; ?></li>
                                                    <li>Roll UNIQUE: <?php echo $hasRollUnique ? '❌ Yes (Problem)' : '✅ No (Good)'; ?></li>
                                                    <li>Empty Emails: <?php echo $emptyEmails; ?> records</li>
                                                    <li>Empty Phones: <?php echo $emptyPhones; ?> records</li>
                                                    <li>Empty Rolls: <?php echo $emptyRolls; ?> records</li>
                                                    <li>Duplicate Regis: <?php echo $duplicateRegis; ?> records</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6>Fix Actions:</h6>
                                                <ul class="mb-0">
                                                    <li>Remove email UNIQUE constraint</li>
                                                    <li>Remove phone UNIQUE constraint</li>
                                                    <li>Remove roll UNIQUE constraint</li>
                                                    <li>Convert empty strings to NULL</li>
                                                    <li>Remove duplicate registrations</li>
                                                    <li>Allow bulk upload</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <form method="POST" onsubmit="return confirm('Are you sure you want to modify the database structure?');">
                                    <button type="submit" name="fix_constraint" value="1" class="btn btn-danger btn-lg">
                                        <i class="fas fa-tools me-2"></i>Fix Database Constraints
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>What This Does
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Removes email UNIQUE constraint</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Removes phone UNIQUE constraint</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Removes roll UNIQUE constraint</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Converts empty values to NULL</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Removes duplicate registrations</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Allows bulk CSV upload</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Table Structure</h6>
                            </div>
                            <div class="card-body">
                                <div style="max-height: 300px; overflow-y: auto;">
                                    <pre style="font-size: 10px;"><?php echo htmlspecialchars(substr($createTable, 0, 1000)); ?><?php echo strlen($createTable) > 1000 ? '...' : ''; ?></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="unique-regi-upload.php" class="btn btn-outline-primary">
                        <i class="fas fa-upload me-2"></i>Back to Upload
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-list me-2"></i>Students List
                    </a>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
