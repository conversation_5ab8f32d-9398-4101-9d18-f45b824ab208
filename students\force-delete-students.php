<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

// Force delete all students - simple and direct approach
if ($_POST['confirm_delete'] ?? false) {
    try {
        executeQuery("START TRANSACTION");
        
        // Get all students first
        $allStudents = fetchAll("SELECT * FROM students");
        $deletedCount = 0;
        $deletedNames = [];
        
        foreach ($allStudents as $student) {
            $studentId = $student['id'];
            $studentName = $student['name'];
            
            // Delete related data
            executeQuery("DELETE FROM fee_payments WHERE student_id = ?", [$studentId]);
            executeQuery("DELETE FROM exam_results WHERE student_id = ?", [$studentId]);
            
            // Delete student picture if exists
            if (!empty($student['picture'])) {
                $picturePath = '../uploads/students/' . $student['picture'];
                if (file_exists($picturePath)) {
                    unlink($picturePath);
                }
            }
            
            // Delete student record
            executeQuery("DELETE FROM students WHERE id = ?", [$studentId]);
            
            $deletedCount++;
            $deletedNames[] = $studentName . " (ID: $studentId)";
        }
        
        executeQuery("COMMIT");
        
        $result = [
            'success' => true,
            'count' => $deletedCount,
            'names' => $deletedNames,
            'message' => "সফলভাবে $deletedCount জন ছাত্রের তথ্য মুছে ফেলা হয়েছে।"
        ];
        
    } catch (Exception $e) {
        executeQuery("ROLLBACK");
        $result = [
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ];
    }
}

// Get current students for display
$currentStudents = fetchAll("SELECT id, name, roll, regi FROM students ORDER BY id");
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Delete All Students</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-trash-alt me-2"></i>
                            Force Delete All Students
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Current Students Display -->
                        <div class="mb-4">
                            <h5 class="text-primary">
                                <i class="fas fa-users me-2"></i>
                                বর্তমানে ডেটাবেসে ছাত্রদের তালিকা (<?php echo count($currentStudents); ?> জন):
                            </h5>
                            
                            <?php if (empty($currentStudents)): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    কোন ছাত্রের তথ্য পাওয়া যায়নি।
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>ID</th>
                                                <th>নাম</th>
                                                <th>রোল</th>
                                                <th>রেজিস্ট্রেশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($currentStudents as $student): ?>
                                            <tr>
                                                <td><?php echo $student['id']; ?></td>
                                                <td><?php echo htmlspecialchars($student['name']); ?></td>
                                                <td><?php echo htmlspecialchars($student['roll']); ?></td>
                                                <td><?php echo htmlspecialchars($student['regi']); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Result Display -->
                        <?php if (isset($result)): ?>
                            <?php if ($result['success']): ?>
                                <div class="alert alert-success">
                                    <h5 class="text-success mb-3">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <?php echo $result['message']; ?>
                                    </h5>
                                    
                                    <?php if (!empty($result['names'])): ?>
                                        <h6 class="text-success mb-2">মুছে ফেলা ছাত্রদের তালিকা:</h6>
                                        <ul class="mb-0">
                                            <?php foreach ($result['names'] as $name): ?>
                                                <li><?php echo htmlspecialchars($name); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mt-3">
                                    <a href="index.php" class="btn btn-primary">
                                        <i class="fas fa-list me-2"></i>ছাত্রদের তালিকা দেখুন
                                    </a>
                                    <a href="../index.php" class="btn btn-secondary ms-2">
                                        <i class="fas fa-home me-2"></i>ড্যাশবোর্ড
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo $result['message']; ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <!-- Delete Form -->
                            <?php if (!empty($currentStudents)): ?>
                                <form method="POST" onsubmit="return confirm('আপনি কি নিশ্চিত যে সব ছাত্রের তথ্য মুছে ফেলতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না!');">
                                    <div class="alert alert-warning">
                                        <h6 class="text-warning mb-3">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            সতর্কতা: এই অপশনটি সব ছাত্রের তথ্য স্থায়ীভাবে মুছে ফেলবে!
                                        </h6>
                                        <ul class="mb-3">
                                            <li>সব ছাত্রের ব্যক্তিগত তথ্য</li>
                                            <li>পরীক্ষার ফলাফল</li>
                                            <li>ফি পেমেন্ট রেকর্ড</li>
                                            <li>প্রোফাইল ছবি</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" name="confirm_delete" value="1" class="btn btn-danger btn-lg">
                                            <i class="fas fa-trash-alt me-2"></i>
                                            সব ছাত্রের তথ্য মুছে ফেলুন (<?php echo count($currentStudents); ?> জন)
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <div class="mt-4 text-center">
                            <a href="index.php" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>ছাত্রদের তালিকায় ফিরে যান
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
