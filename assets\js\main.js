// SKUL Education Management System - Main JavaScript

$(document).ready(function() {
    // Initialize components
    initializeComponents();
    
    // Setup event listeners
    setupEventListeners();
    
    // Initialize tooltips and popovers
    initializeBootstrapComponents();
});

// Initialize main components
function initializeComponents() {
    // Initialize sidebar
    initializeSidebar();
    
    // Initialize data tables
    initializeDataTables();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize file uploads
    initializeFileUploads();
    
    // Initialize search functionality
    initializeSearch();
}

// Setup event listeners
function setupEventListeners() {
    // Sidebar toggle
    $(document).on('click', '.sidebar-toggle', function() {
        toggleSidebar();
    });
    
    // Form submissions
    $(document).on('submit', '.ajax-form', function(e) {
        e.preventDefault();
        handleAjaxForm($(this));
    });
    
    // Delete confirmations
    $(document).on('click', '.delete-btn', function(e) {
        e.preventDefault();
        confirmDelete($(this));
    });
    
    // Quick actions
    $(document).on('click', '.quick-action', function() {
        handleQuickAction($(this));
    });
    
    // Search input
    $(document).on('input', '.search-input', function() {
        handleSearch($(this));
    });
    
    // Filter changes
    $(document).on('change', '.filter-select', function() {
        handleFilterChange($(this));
    });
    
    // Auto-hide alerts
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}

// Initialize Bootstrap components
function initializeBootstrapComponents() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Sidebar functionality
function initializeSidebar() {
    // Set active menu item
    const currentPath = window.location.pathname;
    $('.sidebar-menu a').each(function() {
        const href = $(this).attr('href');
        if (currentPath.includes(href) && href !== '/') {
            $(this).addClass('active');
            $(this).closest('.nav-item').addClass('active');
        }
    });
}

function toggleSidebar() {
    $('.sidebar').toggleClass('show');
    $('.main-content').toggleClass('sidebar-open');
}

// Data Tables
function initializeDataTables() {
    if ($.fn.DataTable) {
        $('.data-table').DataTable({
            responsive: true,
            pageLength: 25,
            language: {
                search: "অনুসন্ধান:",
                lengthMenu: "_MENU_ টি এন্ট্রি দেখান",
                info: "_TOTAL_ টির মধ্যে _START_ থেকে _END_ দেখানো হচ্ছে",
                infoEmpty: "কোনো এন্ট্রি নেই",
                infoFiltered: "(_MAX_ টি এন্ট্রি থেকে ফিল্টার করা হয়েছে)",
                paginate: {
                    first: "প্রথম",
                    last: "শেষ",
                    next: "পরবর্তী",
                    previous: "পূর্ববর্তী"
                },
                emptyTable: "টেবিলে কোনো ডেটা নেই",
                zeroRecords: "কোনো মিল পাওয়া যায়নি"
            },
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
            order: [[0, 'desc']]
        });
    }
}

// Form validation
function initializeFormValidation() {
    // Custom validation rules
    if ($.validator) {
        $.validator.addMethod("bangladeshiPhone", function(value, element) {
            return this.optional(element) || /^(\+88)?01[3-9]\d{8}$/.test(value);
        }, "অনুগ্রহ করে একটি বৈধ বাংলাদেশী ফোন নম্বর দিন");
        
        // Initialize form validation
        $('.validate-form').validate({
            errorClass: 'is-invalid',
            validClass: 'is-valid',
            errorElement: 'div',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group, .mb-3').append(error);
            },
            highlight: function(element) {
                $(element).addClass('is-invalid').removeClass('is-valid');
            },
            unhighlight: function(element) {
                $(element).addClass('is-valid').removeClass('is-invalid');
            }
        });
    }
}

// File upload functionality
function initializeFileUploads() {
    $('.file-upload').on('change', function() {
        const file = this.files[0];
        const preview = $(this).siblings('.file-preview');
        
        if (file) {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.html(`<img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px;">`);
                };
                reader.readAsDataURL(file);
            } else {
                preview.html(`<p class="text-muted">ফাইল নির্বাচিত: ${file.name}</p>`);
            }
        }
    });
}

// Search functionality
function initializeSearch() {
    let searchTimeout;
    
    $('.live-search').on('input', function() {
        const searchTerm = $(this).val();
        const targetTable = $(this).data('target');
        
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            performLiveSearch(searchTerm, targetTable);
        }, 300);
    });
}

function performLiveSearch(term, target) {
    if (term.length >= 2) {
        $.ajax({
            url: 'api/search.php',
            method: 'GET',
            data: {
                term: term,
                target: target
            },
            dataType: 'json',
            success: function(data) {
                updateSearchResults(data, target);
            },
            error: function() {
                showNotification('অনুসন্ধানে সমস্যা হয়েছে', 'error');
            }
        });
    }
}

function updateSearchResults(data, target) {
    const container = $(`.search-results[data-target="${target}"]`);
    let html = '';
    
    if (data.length > 0) {
        data.forEach(function(item) {
            html += createSearchResultItem(item, target);
        });
    } else {
        html = '<p class="text-muted text-center">কোনো ফলাফল পাওয়া যায়নি</p>';
    }
    
    container.html(html);
}

function createSearchResultItem(item, target) {
    switch(target) {
        case 'students':
            return `
                <div class="search-result-item" data-id="${item.id}">
                    <div class="d-flex align-items-center">
                        <img src="${item.picture || 'assets/images/default-avatar.svg'}" class="rounded-circle me-3" width="40" height="40">
                        <div>
                            <h6 class="mb-1">${item.name}</h6>
                            <small class="text-muted">${item.roll} • ${item.group || 'N/A'}</small>
                        </div>
                    </div>
                </div>
            `;
        case 'teachers':
            return `
                <div class="search-result-item" data-id="${item.id}">
                    <div class="d-flex align-items-center">
                        <img src="${item.picture || 'assets/images/default-avatar.svg'}" class="rounded-circle me-3" width="40" height="40">
                        <div>
                            <h6 class="mb-1">${item.name}</h6>
                            <small class="text-muted">${item.designation} • ${item.department}</small>
                        </div>
                    </div>
                </div>
            `;
        default:
            return '';
    }
}

// AJAX form handling
function handleAjaxForm(form) {
    const formData = new FormData(form[0]);
    const url = form.attr('action') || window.location.href;
    const method = form.attr('method') || 'POST';
    
    // Show loading
    showLoading();
    
    $.ajax({
        url: url,
        method: method,
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            hideLoading();
            
            if (response.success) {
                showNotification(response.message, 'success');
                
                // Handle redirect
                if (response.redirect) {
                    setTimeout(function() {
                        window.location.href = response.redirect;
                    }, 1500);
                }
                
                // Reset form if needed
                if (response.reset_form) {
                    form[0].reset();
                }
                
                // Refresh data if needed
                if (response.refresh_data) {
                    refreshPageData();
                }
            } else {
                showNotification(response.message, 'error');
                
                // Show validation errors
                if (response.errors) {
                    showValidationErrors(response.errors, form);
                }
            }
        },
        error: function(xhr) {
            hideLoading();
            
            let message = 'একটি ত্রুটি ঘটেছে';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            
            showNotification(message, 'error');
        }
    });
}

// Delete confirmation
function confirmDelete(button) {
    const message = button.data('message') || 'আপনি কি নিশ্চিত যে এটি মুছে ফেলতে চান?';
    const url = button.attr('href') || button.data('url');
    
    if (confirm(message)) {
        $.ajax({
            url: url,
            method: 'DELETE',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    
                    // Remove row from table
                    button.closest('tr').fadeOut(function() {
                        $(this).remove();
                    });
                    
                    // Refresh page if needed
                    if (response.refresh) {
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    }
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('মুছে ফেলতে সমস্যা হয়েছে', 'error');
            }
        });
    }
}

// Quick actions
function handleQuickAction(button) {
    const action = button.data('action');
    const url = button.data('url');
    const data = button.data('params') || {};
    
    $.ajax({
        url: url,
        method: 'POST',
        data: data,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                
                if (response.redirect) {
                    window.location.href = response.redirect;
                }
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('কার্যক্রম সম্পন্ন করতে সমস্যা হয়েছে', 'error');
        }
    });
}

// Filter handling
function handleFilterChange(select) {
    const form = select.closest('form');
    if (form.length) {
        form.submit();
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const icon = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    };
    
    const alert = $(`
        <div class="alert ${alertClass[type]} alert-dismissible fade show notification-alert" role="alert">
            <i class="${icon[type]} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    // Remove existing notifications
    $('.notification-alert').remove();
    
    // Add new notification
    $('body').prepend(alert);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        alert.fadeOut();
    }, 5000);
}

// Loading functions
function showLoading() {
    if ($('.loading-overlay').length === 0) {
        $('body').append(`
            <div class="loading-overlay">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">লোড হচ্ছে...</span>
                </div>
            </div>
        `);
    }
}

function hideLoading() {
    $('.loading-overlay').remove();
}

// Validation error display
function showValidationErrors(errors, form) {
    // Clear previous errors
    form.find('.is-invalid').removeClass('is-invalid');
    form.find('.invalid-feedback').remove();
    
    // Show new errors
    $.each(errors, function(field, messages) {
        const input = form.find(`[name="${field}"]`);
        if (input.length) {
            input.addClass('is-invalid');
            
            const errorDiv = $(`<div class="invalid-feedback">${messages[0]}</div>`);
            input.closest('.form-group, .mb-3').append(errorDiv);
        }
    });
}

// Refresh page data
function refreshPageData() {
    // Refresh statistics if on dashboard
    if (window.location.pathname === '/' || window.location.pathname.includes('dashboard')) {
        refreshDashboardStats();
    }
    
    // Refresh data tables
    if ($.fn.DataTable) {
        $('.data-table').DataTable().ajax.reload();
    }
}

// Utility functions
function formatCurrency(amount) {
    return '৳' + parseFloat(amount).toLocaleString('en-BD', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function formatDate(dateString, format = 'dd/mm/yyyy') {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    
    switch(format) {
        case 'dd/mm/yyyy':
            return `${day}/${month}/${year}`;
        case 'mm/dd/yyyy':
            return `${month}/${day}/${year}`;
        case 'yyyy-mm-dd':
            return `${year}-${month}-${day}`;
        default:
            return `${day}/${month}/${year}`;
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for global use
window.SKUL = {
    showNotification,
    showLoading,
    hideLoading,
    formatCurrency,
    formatDate,
    debounce,
    refreshPageData
};
