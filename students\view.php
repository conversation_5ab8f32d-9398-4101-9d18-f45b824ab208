<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: index.php?error=' . urlencode('ছাত্রের ID প্রদান করা হয়নি।'));
    exit();
}

$studentId = intval($_GET['id']);

// Get student details
$student = fetchRow("SELECT * FROM students WHERE id = ?", [$studentId]);

if (!$student) {
    header('Location: index.php?error=' . urlencode('ছাত্র পাওয়া যায়নি।'));
    exit();
}

// Get user data for navbar
$user = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্রের বিস্তারিত - <?php echo htmlspecialchars($student['name']); ?> - SKUL</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>

<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="page-title">
                                <i class="fas fa-user-graduate me-2"></i>
                                ছাত্রের বিস্তারিত তথ্য
                            </h2>
                            <p class="text-muted"><?php echo htmlspecialchars($student['name']); ?> এর সম্পূর্ণ তথ্য</p>
                        </div>
                        <div class="btn-group">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                            </a>
                            <a href="edit.php?id=<?php echo $student['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-edit me-1"></i> সম্পাদনা
                            </a>
                            <a href="delete.php?id=<?php echo $student['id']; ?>" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i> মুছে ফেলুন
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Student Details Card -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-camera me-2"></i>প্রোফাইল ছবি
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <img src="<?php echo $student['picture'] ? '../uploads/students/' . $student['picture'] : '../assets/images/default-avatar.svg'; ?>"
                                 alt="<?php echo htmlspecialchars($student['name']); ?>" 
                                 class="img-fluid rounded" style="max-width: 250px;">
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>ব্যক্তিগত তথ্য
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>নাম:</strong></td>
                                            <td><?php echo htmlspecialchars($student['name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>রোল:</strong></td>
                                            <td><?php echo htmlspecialchars($student['roll'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>রেজিস্ট্রেশন:</strong></td>
                                            <td><?php echo htmlspecialchars($student['regi'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>গ্রুপ:</strong></td>
                                            <td><?php echo htmlspecialchars($student['group'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>সেশন:</strong></td>
                                            <td><?php echo htmlspecialchars($student['session'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>লিঙ্গ:</strong></td>
                                            <td><?php echo htmlspecialchars($student['gender'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>জন্ম তারিখ:</strong></td>
                                            <td><?php echo $student['dob'] ? date('d/m/Y', strtotime($student['dob'])) : 'N/A'; ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>বাবার নাম:</strong></td>
                                            <td><?php echo htmlspecialchars($student['fname'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>মায়ের নাম:</strong></td>
                                            <td><?php echo htmlspecialchars($student['mname'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ফোন:</strong></td>
                                            <td><?php echo htmlspecialchars($student['phone'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ইমেইল:</strong></td>
                                            <td><?php echo htmlspecialchars($student['email'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ঠিকানা:</strong></td>
                                            <td><?php echo htmlspecialchars($student['address'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>রক্তের গ্রুপ:</strong></td>
                                            <td><?php echo htmlspecialchars($student['blood_group'] ?: 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ধর্ম:</strong></td>
                                            <td><?php echo htmlspecialchars($student['religion'] ?: 'N/A'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Academic Information -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-graduation-cap me-2"></i>একাডেমিক তথ্য
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <p><strong>বিষয় ১:</strong> <?php echo htmlspecialchars($student['sub1'] ?: 'N/A'); ?></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>বিষয় ২:</strong> <?php echo htmlspecialchars($student['sub2'] ?: 'N/A'); ?></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>বিষয় ৩:</strong> <?php echo htmlspecialchars($student['sub3'] ?: 'N/A'); ?></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>৪র্থ বিষয়:</strong> <?php echo htmlspecialchars($student['4th_sub'] ?: 'N/A'); ?></p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <p><strong>ভর্তির তারিখ:</strong> <?php echo $student['admission_date'] ? date('d/m/Y', strtotime($student['admission_date'])) : 'N/A'; ?></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>পূর্বের স্কুল:</strong> <?php echo htmlspecialchars($student['previous_school'] ?: 'N/A'); ?></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>পূর্বের GPA:</strong> <?php echo htmlspecialchars($student['previous_gpa'] ?: 'N/A'); ?></p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <p><strong>অবস্থা:</strong> 
                                        <span class="badge bg-<?php echo $student['status'] === 'Active' ? 'success' : 'secondary'; ?>">
                                            <?php echo htmlspecialchars($student['status']); ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Guardian Information -->
            <?php if ($student['guardian_phone'] || $student['guardian_email'] || $student['guardian_address']): ?>
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-users me-2"></i>অভিভাবকের তথ্য
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <p><strong>ফোন:</strong> <?php echo htmlspecialchars($student['guardian_phone'] ?: 'N/A'); ?></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>ইমেইল:</strong> <?php echo htmlspecialchars($student['guardian_email'] ?: 'N/A'); ?></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>জরুরি যোগাযোগ:</strong> <?php echo htmlspecialchars($student['emergency_contact'] ?: 'N/A'); ?></p>
                                </div>
                            </div>
                            <?php if ($student['guardian_address']): ?>
                            <div class="row">
                                <div class="col-md-12">
                                    <p><strong>ঠিকানা:</strong> <?php echo htmlspecialchars($student['guardian_address']); ?></p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Additional Information -->
            <?php if ($student['medical_info'] || $student['notes']): ?>
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clipboard me-2"></i>অতিরিক্ত তথ্য
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if ($student['medical_info']): ?>
                            <div class="mb-3">
                                <strong>চিকিৎসা সংক্রান্ত তথ্য:</strong>
                                <p><?php echo nl2br(htmlspecialchars($student['medical_info'])); ?></p>
                            </div>
                            <?php endif; ?>
                            <?php if ($student['notes']): ?>
                            <div>
                                <strong>মন্তব্য:</strong>
                                <p><?php echo nl2br(htmlspecialchars($student['notes'])); ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
