// SKUL Education Management System - Charts JavaScript

// Initialize charts when D<PERSON> is ready
function initializeCharts() {
    initializeFeeCollectionChart();
    initializeStudentsGroupChart();
}

// Monthly Fee Collection Chart
function initializeFeeCollectionChart() {
    const ctx = document.getElementById('monthlyFeeChart');
    if (!ctx) return;
    
    // Get chart data via AJAX
    $.ajax({
        url: 'api/monthly-fee-data.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            createFeeCollectionChart(ctx, data);
        },
        error: function() {
            console.error('Failed to load fee collection data');
            // Use dummy data as fallback
            const dummyData = [0, 15000, 25000, 30000, 35000, 40000, 45000, 50000, 55000, 60000, 65000, 70000];
            createFeeCollectionChart(ctx, dummyData);
        }
    });
}

function createFeeCollectionChart(ctx, data) {
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন', 
                    'জুলাই', 'আগস্ট', 'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর'],
            datasets: [{
                label: 'ফি সংগ্রহ (৳)',
                data: data,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return '৳' + context.parsed.y.toLocaleString('en-BD');
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 12
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return '৳' + value.toLocaleString('en-BD');
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#667eea'
                }
            }
        }
    });
}

// Students by Group Chart
function initializeStudentsGroupChart() {
    const ctx = document.getElementById('studentsGroupChart');
    if (!ctx) return;
    
    // Get chart data via AJAX
    $.ajax({
        url: 'api/students-group-data.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            createStudentsGroupChart(ctx, data);
        },
        error: function() {
            console.error('Failed to load students group data');
            // Use dummy data as fallback
            const dummyData = {
                labels: ['বিজ্ঞান', 'ব্যবসায়', 'মানবিক', 'কারিগরি'],
                data: [150, 120, 80, 50]
            };
            createStudentsGroupChart(ctx, dummyData);
        }
    });
}

function createStudentsGroupChart(ctx, chartData) {
    const colors = [
        '#667eea',
        '#764ba2',
        '#f093fb',
        '#f5576c',
        '#4facfe',
        '#43e97b'
    ];
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.data,
                backgroundColor: colors.slice(0, chartData.labels.length),
                borderWidth: 0,
                hoverBorderWidth: 2,
                hoverBorderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12
                        },
                        color: '#6c757d'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' জন (' + percentage + '%)';
                        }
                    }
                }
            },
            cutout: '60%',
            elements: {
                arc: {
                    borderWidth: 0
                }
            }
        }
    });
}

// Refresh dashboard charts
function refreshDashboardCharts() {
    // Destroy existing charts
    Chart.helpers.each(Chart.instances, function(instance) {
        instance.destroy();
    });
    
    // Reinitialize charts
    initializeCharts();
}

// Refresh dashboard stats
function refreshDashboardStats() {
    $.ajax({
        url: 'api/dashboard-stats.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            // Update statistics cards
            $('#total-students').text(data.total_students.toLocaleString('en-BD'));
            $('#total-teachers').text(data.total_teachers.toLocaleString('en-BD'));
            $('#total-exams').text(data.total_exams.toLocaleString('en-BD'));
            $('#monthly-collection').text('৳' + data.monthly_collection.toLocaleString('en-BD'));
            
            // Add animation effect
            $('.stats-number').addClass('animate__animated animate__pulse');
            setTimeout(function() {
                $('.stats-number').removeClass('animate__animated animate__pulse');
            }, 1000);
        },
        error: function() {
            console.error('Failed to refresh dashboard stats');
        }
    });
}

// Export chart as image
function exportChart(chartId, filename) {
    const canvas = document.getElementById(chartId);
    if (!canvas) return;
    
    const url = canvas.toDataURL('image/png');
    const link = document.createElement('a');
    link.download = filename || 'chart.png';
    link.href = url;
    link.click();
}

// Print chart
function printChart(chartId) {
    const canvas = document.getElementById(chartId);
    if (!canvas) return;
    
    const dataUrl = canvas.toDataURL();
    const windowContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Chart Print</title>
            <style>
                body { margin: 0; padding: 20px; text-align: center; }
                img { max-width: 100%; height: auto; }
                @media print {
                    body { margin: 0; }
                    img { max-width: 100%; page-break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <img src="${dataUrl}" alt="Chart">
        </body>
        </html>
    `;
    
    const printWindow = window.open('', '', 'width=800,height=600');
    printWindow.document.open();
    printWindow.document.write(windowContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
}

// Chart utility functions
const ChartUtils = {
    // Generate random colors
    generateColors: function(count) {
        const colors = [];
        for (let i = 0; i < count; i++) {
            colors.push(`hsl(${(i * 360 / count)}, 70%, 60%)`);
        }
        return colors;
    },
    
    // Format currency for charts
    formatCurrency: function(value) {
        return '৳' + value.toLocaleString('en-BD');
    },
    
    // Format percentage
    formatPercentage: function(value, total) {
        return ((value / total) * 100).toFixed(1) + '%';
    },
    
    // Common chart options
    getCommonOptions: function() {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8
                }
            }
        };
    }
};

// Export functions for global use
window.ChartManager = {
    initializeCharts,
    refreshDashboardCharts,
    refreshDashboardStats,
    exportChart,
    printChart,
    ChartUtils
};
