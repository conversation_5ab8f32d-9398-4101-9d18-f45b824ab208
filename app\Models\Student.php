<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Student extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'roll',
        'name',
        'regi',
        'group',
        'fname',
        'mname',
        'gender',
        'dob',
        'session',
        'sub1',
        'sub2',
        'sub3',
        '4th_sub',
        'picture',
        'phone',
        'email',
        'address',
        'blood_group',
        'religion',
        'nationality',
        'admission_date',
        'previous_school',
        'previous_gpa',
        'status',
        'guardian_phone',
        'guardian_email',
        'guardian_address',
        'emergency_contact',
        'medical_info',
        'notes'
    ];

    protected $casts = [
        'dob' => 'date',
        'admission_date' => 'date',
        'previous_gpa' => 'decimal:2'
    ];

    protected $dates = ['deleted_at'];

    // Relationships
    public function examResults()
    {
        return $this->hasMany(ExamResult::class);
    }

    public function feePayments()
    {
        return $this->hasMany(FeePayment::class);
    }

    public function user()
    {
        return $this->hasOne(User::class, 'user_id')->where('user_type', 'student');
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->name;
    }

    public function getAgeAttribute()
    {
        return $this->dob ? $this->dob->age : null;
    }

    public function getPictureUrlAttribute()
    {
        if ($this->picture) {
            return asset('storage/' . $this->picture);
        }
        return asset('images/default-avatar.png');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    public function scopeBySession($query, $session)
    {
        return $query->where('session', $session);
    }

    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('roll', 'like', "%{$search}%")
              ->orWhere('regi', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        });
    }

    // Mutators
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = ucwords(strtolower($value));
    }

    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = strtolower($value);
    }

    // Helper methods
    public function generateRoll()
    {
        $prefix = config('education.student.roll_prefix', 'STU');
        $year = date('y');
        $lastStudent = static::where('roll', 'like', $prefix . $year . '%')
                           ->orderBy('roll', 'desc')
                           ->first();
        
        if ($lastStudent) {
            $lastNumber = (int) substr($lastStudent->roll, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    public function generateRegistration()
    {
        $prefix = config('education.student.registration_prefix', 'REG');
        $year = date('Y');
        $lastStudent = static::where('regi', 'like', $prefix . $year . '%')
                           ->orderBy('regi', 'desc')
                           ->first();
        
        if ($lastStudent) {
            $lastNumber = (int) substr($lastStudent->regi, -6);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . $year . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }

    public function getTotalFeesPaid()
    {
        return $this->feePayments()->where('status', 'Paid')->sum('amount_paid');
    }

    public function getPendingFees()
    {
        $totalFees = Fee::where('session', $this->session)
                       ->where(function ($q) {
                           $q->where('group', $this->group)
                             ->orWhereNull('group');
                       })
                       ->where('status', 'Active')
                       ->sum('amount');
        
        $paidFees = $this->getTotalFeesPaid();
        
        return $totalFees - $paidFees;
    }
}
