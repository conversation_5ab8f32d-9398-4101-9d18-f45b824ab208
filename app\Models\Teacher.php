<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Teacher extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'employee_id',
        'name',
        'email',
        'phone',
        'gender',
        'dob',
        'address',
        'designation',
        'department',
        'qualification',
        'specialization',
        'joining_date',
        'salary',
        'blood_group',
        'religion',
        'nationality',
        'nid',
        'picture',
        'status',
        'emergency_contact',
        'experience',
        'notes'
    ];

    protected $casts = [
        'dob' => 'date',
        'joining_date' => 'date',
        'salary' => 'decimal:2'
    ];

    protected $dates = ['deleted_at'];

    // Relationships
    public function routines()
    {
        return $this->hasMany(Routine::class);
    }

    public function committeeMembers()
    {
        return $this->hasMany(CommitteeMember::class);
    }

    public function notices()
    {
        return $this->hasMany(Notice::class, 'created_by');
    }

    public function feeCollections()
    {
        return $this->hasMany(FeePayment::class, 'collected_by');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'user_id')->where('user_type', 'teacher');
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->name;
    }

    public function getAgeAttribute()
    {
        return $this->dob ? $this->dob->age : null;
    }

    public function getPictureUrlAttribute()
    {
        if ($this->picture) {
            return asset('storage/' . $this->picture);
        }
        return asset('images/default-avatar.png');
    }

    public function getExperienceYearsAttribute()
    {
        return $this->joining_date ? $this->joining_date->diffInYears(now()) : 0;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    public function scopeByDepartment($query, $department)
    {
        return $query->where('department', $department);
    }

    public function scopeByDesignation($query, $designation)
    {
        return $query->where('designation', $designation);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('employee_id', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('designation', 'like', "%{$search}%")
              ->orWhere('department', 'like', "%{$search}%");
        });
    }

    // Mutators
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = ucwords(strtolower($value));
    }

    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = strtolower($value);
    }

    // Helper methods
    public function generateEmployeeId()
    {
        $prefix = config('education.teacher.employee_prefix', 'EMP');
        $year = date('y');
        $lastTeacher = static::where('employee_id', 'like', $prefix . $year . '%')
                           ->orderBy('employee_id', 'desc')
                           ->first();
        
        if ($lastTeacher) {
            $lastNumber = (int) substr($lastTeacher->employee_id, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    public function getActiveCommittees()
    {
        return $this->committeeMembers()
                   ->with('committee')
                   ->whereHas('committee', function ($q) {
                       $q->where('status', 'Active');
                   })
                   ->where('status', 'Active')
                   ->get();
    }

    public function getTotalFeeCollected($month = null, $year = null)
    {
        $query = $this->feeCollections()->where('status', 'Paid');
        
        if ($month && $year) {
            $query->whereMonth('payment_date', $month)
                  ->whereYear('payment_date', $year);
        } elseif ($year) {
            $query->whereYear('payment_date', $year);
        }
        
        return $query->sum('amount_paid');
    }
}
