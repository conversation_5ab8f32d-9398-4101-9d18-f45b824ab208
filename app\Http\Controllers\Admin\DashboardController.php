<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\Exam;
use App\Models\Fee;
use App\Models\FeePayment;
use App\Models\Notice;
use App\Models\Committee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Get current academic year
        $currentYear = config('education.academic.default_year', date('Y'));
        
        // Basic statistics
        $stats = [
            'total_students' => Student::count(),
            'active_students' => Student::where('status', 'Active')->count(),
            'total_teachers' => Teacher::count(),
            'active_teachers' => Teacher::where('status', 'Active')->count(),
            'total_exams' => Exam::count(),
            'upcoming_exams' => Exam::where('start_date', '>', now())->count(),
            'total_notices' => Notice::where('status', 'Published')->count(),
            'active_committees' => Committee::where('status', 'Active')->count(),
        ];
        
        // Student statistics by group
        $studentsByGroup = Student::where('status', 'Active')
                                 ->where('session', $currentYear)
                                 ->select('group', DB::raw('count(*) as total'))
                                 ->groupBy('group')
                                 ->pluck('total', 'group')
                                 ->toArray();
        
        // Student statistics by gender
        $studentsByGender = Student::where('status', 'Active')
                                  ->select('gender', DB::raw('count(*) as total'))
                                  ->groupBy('gender')
                                  ->pluck('total', 'gender')
                                  ->toArray();
        
        // Fee collection statistics
        $currentMonth = now()->month;
        $currentMonthYear = now()->year;
        
        $feeStats = [
            'total_collected_this_month' => FeePayment::whereMonth('payment_date', $currentMonth)
                                                     ->whereYear('payment_date', $currentMonthYear)
                                                     ->where('status', 'Paid')
                                                     ->sum('amount_paid'),
            'total_collected_this_year' => FeePayment::whereYear('payment_date', $currentMonthYear)
                                                    ->where('status', 'Paid')
                                                    ->sum('amount_paid'),
            'pending_fees' => $this->calculatePendingFees($currentYear),
        ];
        
        // Recent activities
        $recentStudents = Student::latest()->take(5)->get();
        $recentNotices = Notice::where('status', 'Published')
                              ->latest('publish_date')
                              ->take(5)
                              ->get();
        $recentExams = Exam::latest()->take(5)->get();
        
        // Upcoming events
        $upcomingExams = Exam::where('start_date', '>', now())
                            ->orderBy('start_date')
                            ->take(5)
                            ->get();
        
        // Monthly fee collection chart data
        $monthlyCollections = $this->getMonthlyFeeCollections($currentMonthYear);
        
        // Student enrollment trend
        $enrollmentTrend = $this->getStudentEnrollmentTrend();
        
        return view('admin.dashboard', compact(
            'stats',
            'studentsByGroup',
            'studentsByGender',
            'feeStats',
            'recentStudents',
            'recentNotices',
            'recentExams',
            'upcomingExams',
            'monthlyCollections',
            'enrollmentTrend'
        ));
    }
    
    private function calculatePendingFees($session)
    {
        $totalFees = Fee::where('session', $session)
                       ->where('status', 'Active')
                       ->sum('amount');
        
        $totalStudents = Student::where('session', $session)
                               ->where('status', 'Active')
                               ->count();
        
        $expectedTotal = $totalFees * $totalStudents;
        
        $collectedTotal = FeePayment::whereHas('student', function ($query) use ($session) {
                                      $query->where('session', $session);
                                  })
                                  ->where('status', 'Paid')
                                  ->sum('amount_paid');
        
        return $expectedTotal - $collectedTotal;
    }
    
    private function getMonthlyFeeCollections($year)
    {
        $collections = FeePayment::whereYear('payment_date', $year)
                                ->where('status', 'Paid')
                                ->select(
                                    DB::raw('MONTH(payment_date) as month'),
                                    DB::raw('SUM(amount_paid) as total')
                                )
                                ->groupBy('month')
                                ->orderBy('month')
                                ->pluck('total', 'month')
                                ->toArray();
        
        // Fill missing months with 0
        $monthlyData = [];
        for ($i = 1; $i <= 12; $i++) {
            $monthlyData[] = $collections[$i] ?? 0;
        }
        
        return $monthlyData;
    }
    
    private function getStudentEnrollmentTrend()
    {
        $currentYear = date('Y');
        $years = [];
        $enrollments = [];
        
        for ($i = 4; $i >= 0; $i--) {
            $year = $currentYear - $i;
            $years[] = $year;
            $enrollments[] = Student::where('session', $year)->count();
        }
        
        return [
            'years' => $years,
            'enrollments' => $enrollments
        ];
    }
    
    public function profile()
    {
        $user = auth()->user();
        return view('admin.profile', compact('user'));
    }
    
    public function updateProfile(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);
        
        $data = $request->only(['name', 'email']);
        
        if ($request->hasFile('avatar')) {
            // Delete old avatar
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            $data['avatar'] = $request->file('avatar')->store('avatars', 'public');
        }
        
        $user->update($data);
        
        return redirect()->back()->with('success', 'Profile updated successfully.');
    }
    
    public function settings()
    {
        $settings = [
            'institution_name' => config('education.institution.name'),
            'institution_address' => config('education.institution.address'),
            'institution_phone' => config('education.institution.phone'),
            'institution_email' => config('education.institution.email'),
            'default_academic_year' => config('education.academic.default_year'),
        ];
        
        return view('admin.settings', compact('settings'));
    }
    
    public function updateSettings(Request $request)
    {
        $request->validate([
            'institution_name' => 'required|string|max:255',
            'institution_address' => 'required|string|max:500',
            'institution_phone' => 'required|string|max:20',
            'institution_email' => 'required|email|max:255',
            'default_academic_year' => 'required|string|max:10',
        ]);
        
        // Here you would typically update the configuration
        // For now, we'll just show a success message
        
        return redirect()->back()->with('success', 'Settings updated successfully.');
    }
}
