// SKUL Education Management System - Dashboard JavaScript

$(document).ready(function() {
    // Initialize dashboard components
    initializeDashboard();
    
    // Setup auto-refresh
    setupAutoRefresh();
    
    // Initialize real-time updates
    initializeRealTimeUpdates();
});

function initializeDashboard() {
    // Load recent activities
    loadRecentStudents();
    loadRecentNotices();
    
    // Initialize charts
    if (typeof initializeCharts === 'function') {
        initializeCharts();
    }
    
    // Setup quick actions
    setupQuickActions();
    
    // Initialize counters animation
    animateCounters();
}

function loadRecentStudents() {
    $.ajax({
        url: 'api/recent-students.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            displayRecentStudents(data);
        },
        error: function() {
            $('#recent-students-list').html('<p class="text-danger text-center">ডেটা লোড করতে সমস্যা হয়েছে</p>');
        }
    });
}

function displayRecentStudents(students) {
    let html = '';
    
    if (students.length > 0) {
        students.forEach(function(student) {
            html += `
                <div class="activity-item">
                    <div class="activity-avatar">
                        <img src="${student.picture || 'assets/images/default-avatar.svg'}"
                             alt="${student.name}" class="rounded-circle" width="50" height="50">
                    </div>
                    <div class="activity-content">
                        <h6 class="activity-title">${student.name}</h6>
                        <p class="activity-desc">${student.roll} • ${student.group || 'N/A'}</p>
                        <small class="activity-time">${student.created_at}</small>
                    </div>
                    <div class="activity-status">
                        <span class="badge bg-success">সক্রিয়</span>
                    </div>
                </div>
            `;
        });
    } else {
        html = '<p class="text-muted text-center">কোনো সাম্প্রতিক ছাত্র-ছাত্রী নেই</p>';
    }
    
    $('#recent-students-list').html(html);
}

function loadRecentNotices() {
    $.ajax({
        url: 'api/recent-notices.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            displayRecentNotices(data);
        },
        error: function() {
            $('#recent-notices-list').html('<p class="text-danger text-center">ডেটা লোড করতে সমস্যা হয়েছে</p>');
        }
    });
}

function displayRecentNotices(notices) {
    let html = '';
    
    if (notices.length > 0) {
        notices.forEach(function(notice) {
            html += `
                <div class="activity-item">
                    <div class="activity-content">
                        <h6 class="activity-title">${notice.title}</h6>
                        <p class="activity-desc">${notice.content.substring(0, 100)}...</p>
                        <small class="activity-time">${notice.publish_date}</small>
                    </div>
                    <div class="activity-status">
                        <span class="badge bg-${notice.priority_class}">${notice.priority}</span>
                    </div>
                </div>
            `;
        });
    } else {
        html = '<p class="text-muted text-center">কোনো সাম্প্রতিক নোটিশ নেই</p>';
    }
    
    $('#recent-notices-list').html(html);
}

function setupQuickActions() {
    $('.quick-action-btn').on('click', function(e) {
        e.preventDefault();
        
        const href = $(this).attr('href');
        if (href && href !== '#') {
            // Add loading effect
            $(this).addClass('loading');
            
            // Navigate after short delay for visual feedback
            setTimeout(function() {
                window.location.href = href;
            }, 200);
        }
    });
}

function animateCounters() {
    $('.stats-number').each(function() {
        const $this = $(this);
        const countTo = parseInt($this.text().replace(/,/g, ''));
        
        if (isNaN(countTo)) return;
        
        $({ countNum: 0 }).animate({
            countNum: countTo
        }, {
            duration: 2000,
            easing: 'swing',
            step: function() {
                $this.text(Math.floor(this.countNum).toLocaleString('en-BD'));
            },
            complete: function() {
                $this.text(countTo.toLocaleString('en-BD'));
            }
        });
    });
}

function setupAutoRefresh() {
    // Refresh dashboard data every 5 minutes
    setInterval(function() {
        refreshDashboardData();
    }, 5 * 60 * 1000);
    
    // Refresh stats every 30 seconds
    setInterval(function() {
        refreshStats();
    }, 30 * 1000);
}

function refreshDashboardData() {
    loadRecentStudents();
    loadRecentNotices();
    
    // Refresh charts if function exists
    if (typeof refreshDashboardCharts === 'function') {
        refreshDashboardCharts();
    }
}

function refreshStats() {
    $.ajax({
        url: 'api/dashboard-stats.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            updateStatsCards(data);
        },
        error: function() {
            console.error('Failed to refresh stats');
        }
    });
}

function updateStatsCards(data) {
    // Update with animation
    updateStatCard('#total-students', data.total_students);
    updateStatCard('#total-teachers', data.total_teachers);
    updateStatCard('#total-exams', data.total_exams);
    updateStatCard('#monthly-collection', data.monthly_collection, '৳');
}

function updateStatCard(selector, newValue, prefix = '') {
    const $element = $(selector);
    const currentValue = parseInt($element.text().replace(/[^0-9]/g, ''));
    
    if (currentValue !== newValue) {
        $element.addClass('updating');
        
        setTimeout(function() {
            $element.text(prefix + newValue.toLocaleString('en-BD'));
            $element.removeClass('updating').addClass('updated');
            
            setTimeout(function() {
                $element.removeClass('updated');
            }, 1000);
        }, 300);
    }
}

function initializeRealTimeUpdates() {
    // Check for real-time updates every 10 seconds
    setInterval(function() {
        checkForUpdates();
    }, 10 * 1000);
}

function checkForUpdates() {
    $.ajax({
        url: 'api/check-updates.php',
        method: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data.has_updates) {
                showUpdateNotification(data.updates);
            }
        },
        error: function() {
            // Silently fail for real-time updates
        }
    });
}

function showUpdateNotification(updates) {
    let message = '';
    
    if (updates.new_students > 0) {
        message += `${updates.new_students} নতুন ছাত্র-ছাত্রী যোগ হয়েছে। `;
    }
    
    if (updates.new_notices > 0) {
        message += `${updates.new_notices} নতুন নোটিশ প্রকাশিত হয়েছে। `;
    }
    
    if (updates.new_payments > 0) {
        message += `${updates.new_payments} নতুন ফি পেমেন্ট এসেছে। `;
    }
    
    if (message) {
        showNotification(message + 'পেজ রিফ্রেশ করুন।', 'info');
        
        // Add refresh button to notification
        $('.notification-alert').append(`
            <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="location.reload()">
                রিফ্রেশ করুন
            </button>
        `);
    }
}

// Dashboard utility functions
function exportDashboardData() {
    showLoading();
    
    $.ajax({
        url: 'api/export-dashboard.php',
        method: 'GET',
        success: function(data) {
            hideLoading();
            
            if (data.success) {
                // Download the file
                const link = document.createElement('a');
                link.href = data.file_url;
                link.download = data.filename;
                link.click();
                
                showNotification('ড্যাশবোর্ড ডেটা সফলভাবে এক্সপোর্ট হয়েছে', 'success');
            } else {
                showNotification('এক্সপোর্ট করতে সমস্যা হয়েছে', 'error');
            }
        },
        error: function() {
            hideLoading();
            showNotification('এক্সপোর্ট করতে সমস্যা হয়েছে', 'error');
        }
    });
}

function printDashboard() {
    window.print();
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
}

// Keyboard shortcuts
$(document).on('keydown', function(e) {
    // Ctrl + R: Refresh dashboard
    if (e.ctrlKey && e.keyCode === 82) {
        e.preventDefault();
        refreshDashboardData();
        showNotification('ড্যাশবোর্ড রিফ্রেশ করা হয়েছে', 'success');
    }
    
    // Ctrl + E: Export dashboard
    if (e.ctrlKey && e.keyCode === 69) {
        e.preventDefault();
        exportDashboardData();
    }
    
    // F11: Toggle fullscreen
    if (e.keyCode === 122) {
        e.preventDefault();
        toggleFullscreen();
    }
});

// CSS for animations
const dashboardStyles = `
<style>
.stats-number.updating {
    opacity: 0.5;
    transform: scale(0.95);
    transition: all 0.3s ease;
}

.stats-number.updated {
    opacity: 1;
    transform: scale(1.05);
    transition: all 0.3s ease;
}

.quick-action-btn.loading {
    opacity: 0.7;
    transform: scale(0.98);
    pointer-events: none;
}

.activity-item {
    transition: all 0.3s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 1s ease-in-out;
}
</style>
`;

// Inject styles
$('head').append(dashboardStyles);

// Export dashboard functions
window.Dashboard = {
    refreshDashboardData,
    refreshStats,
    exportDashboardData,
    printDashboard,
    toggleFullscreen,
    animateCounters
};
