<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

// Test single insert
if ($_POST['test_insert'] ?? false) {
    try {
        global $pdo;
        
        // Test data
        $testData = [
            'roll' => 'TEST001',
            'name' => 'Test Student',
            'regi' => 'TEST2024001',
            'group' => 'Science',
            'fname' => 'Test Father',
            'mname' => 'Test Mother',
            'gender' => 'Male',
            'dob' => null,
            'session' => '2024',
            'sub1' => null,
            'sub2' => null,
            'sub3' => null,
            '4th_sub' => null,
            'phone' => null,
            'email' => null,
            'address' => null,
            'blood_group' => null,
            'religion' => null,
            'nationality' => 'Bangladeshi',
            'admission_date' => null,
            'status' => 'Active'
        ];
        
        $sql = "INSERT INTO students (roll, name, regi, `group`, fname, mname, gender, dob, session, 
                sub1, sub2, sub3, `4th_sub`, phone, email, address, blood_group, religion, 
                nationality, admission_date, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $params = [
            $testData['roll'], $testData['name'], $testData['regi'], $testData['group'],
            $testData['fname'], $testData['mname'], $testData['gender'],
            $testData['dob'], $testData['session'], $testData['sub1'],
            $testData['sub2'], $testData['sub3'], $testData['4th_sub'],
            $testData['phone'], $testData['email'], $testData['address'],
            $testData['blood_group'], $testData['religion'], $testData['nationality'],
            $testData['admission_date'], $testData['status']
        ];
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($params);
        
        if ($result) {
            $affectedRows = $stmt->rowCount();
            $message = "✓ Success! Affected rows: $affectedRows";
            $messageType = 'success';
        } else {
            $errorInfo = $stmt->errorInfo();
            $message = "✗ Failed! Error: " . $errorInfo[2];
            $messageType = 'error';
        }
        
    } catch (PDOException $e) {
        $message = "✗ PDO Exception: " . $e->getMessage();
        $messageType = 'error';
    } catch (Exception $e) {
        $message = "✗ General Exception: " . $e->getMessage();
        $messageType = 'error';
    }
}

// Check table structure
try {
    global $pdo;
    $stmt = $pdo->query("DESCRIBE students");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $columns = [];
    $structureError = $e->getMessage();
}

// Get current students count
try {
    $count = fetchRow("SELECT COUNT(*) as count FROM students")['count'];
} catch (Exception $e) {
    $count = 'Error: ' . $e->getMessage();
}

$user = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Insert</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-vial me-2"></i>Test Insert
                    </h1>
                </div>

                <?php if (isset($message)): ?>
                    <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Test Single Insert</h5>
                            </div>
                            <div class="card-body">
                                <p>Current students count: <strong><?php echo $count; ?></strong></p>
                                
                                <form method="POST">
                                    <button type="submit" name="test_insert" value="1" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Test Insert Student
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Students Table Structure</h6>
                            </div>
                            <div class="card-body">
                                <?php if (isset($structureError)): ?>
                                    <div class="alert alert-danger">Error: <?php echo htmlspecialchars($structureError); ?></div>
                                <?php else: ?>
                                    <div style="max-height: 400px; overflow-y: auto;">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Field</th>
                                                    <th>Type</th>
                                                    <th>Null</th>
                                                    <th>Key</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($columns as $col): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($col['Field']); ?></td>
                                                    <td><?php echo htmlspecialchars($col['Type']); ?></td>
                                                    <td><?php echo htmlspecialchars($col['Null']); ?></td>
                                                    <td><?php echo htmlspecialchars($col['Key']); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="enhanced-bulk-upload.php" class="btn btn-outline-primary">
                        <i class="fas fa-upload me-2"></i>Back to Upload
                    </a>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
