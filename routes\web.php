<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\StudentController;
use App\Http\Controllers\Admin\TeacherController;
use App\Http\Controllers\Admin\ExamController;
use App\Http\Controllers\Admin\RoutineController;
use App\Http\Controllers\Admin\FeeController;
use App\Http\Controllers\Admin\CommitteeController;
use App\Http\Controllers\Admin\NoticeController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ReportController;
use App\Http\Controllers\AuthController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected routes
Route::middleware(['auth'])->group(function () {
    
    // Admin routes
    Route::prefix('admin')->name('admin.')->group(function () {
        
        // Dashboard
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
        
        // Students Management
        Route::resource('students', StudentController::class);
        Route::get('/students/bulk-upload', [StudentController::class, 'bulkUpload'])->name('students.bulk-upload');
        Route::post('/students/import', [StudentController::class, 'importStudents'])->name('students.import');
        Route::get('/students/export', [StudentController::class, 'exportStudents'])->name('students.export');
        Route::get('/students/template', [StudentController::class, 'downloadTemplate'])->name('students.template');
        Route::get('/students/{student}/profile', [StudentController::class, 'profile'])->name('students.profile');
        Route::patch('/students/{student}/toggle-status', [StudentController::class, 'toggleStatus'])->name('students.toggle-status');
        
        // Teachers Management
        Route::resource('teachers', TeacherController::class);
        Route::get('/teachers/bulk-upload', [TeacherController::class, 'bulkUpload'])->name('teachers.bulk-upload');
        Route::post('/teachers/import', [TeacherController::class, 'importTeachers'])->name('teachers.import');
        Route::get('/teachers/export', [TeacherController::class, 'exportTeachers'])->name('teachers.export');
        Route::get('/teachers/template', [TeacherController::class, 'downloadTemplate'])->name('teachers.template');
        Route::patch('/teachers/{teacher}/toggle-status', [TeacherController::class, 'toggleStatus'])->name('teachers.toggle-status');
        
        // Exams Management
        Route::resource('exams', ExamController::class);
        Route::get('/exams/{exam}/results', [ExamController::class, 'results'])->name('exams.results');
        Route::post('/exams/{exam}/results', [ExamController::class, 'storeResults'])->name('exams.results.store');
        Route::get('/exams/{exam}/results/import', [ExamController::class, 'importResults'])->name('exams.results.import');
        Route::post('/exams/{exam}/results/import', [ExamController::class, 'storeImportResults'])->name('exams.results.import.store');
        Route::get('/exams/{exam}/results/export', [ExamController::class, 'exportResults'])->name('exams.results.export');
        
        // Routines Management
        Route::resource('routines', RoutineController::class);
        Route::get('/routines/class/{session}/{group?}', [RoutineController::class, 'classRoutine'])->name('routines.class');
        Route::get('/routines/exam/{session}/{group?}', [RoutineController::class, 'examRoutine'])->name('routines.exam');
        Route::post('/routines/bulk-create', [RoutineController::class, 'bulkCreate'])->name('routines.bulk-create');
        
        // Fees Management
        Route::resource('fees', FeeController::class);
        Route::get('/fees/{fee}/payments', [FeeController::class, 'payments'])->name('fees.payments');
        Route::post('/fees/{fee}/payments', [FeeController::class, 'storePayment'])->name('fees.payments.store');
        Route::get('/fees/payments/{payment}/receipt', [FeeController::class, 'receipt'])->name('fees.payments.receipt');
        Route::get('/fees/collections/report', [FeeController::class, 'collectionsReport'])->name('fees.collections.report');
        
        // Committees Management
        Route::resource('committees', CommitteeController::class);
        Route::post('/committees/{committee}/members', [CommitteeController::class, 'addMember'])->name('committees.members.add');
        Route::delete('/committees/{committee}/members/{member}', [CommitteeController::class, 'removeMember'])->name('committees.members.remove');
        Route::patch('/committees/{committee}/dissolve', [CommitteeController::class, 'dissolve'])->name('committees.dissolve');
        
        // Notices Management
        Route::resource('notices', NoticeController::class);
        Route::patch('/notices/{notice}/publish', [NoticeController::class, 'publish'])->name('notices.publish');
        Route::patch('/notices/{notice}/archive', [NoticeController::class, 'archive'])->name('notices.archive');
        Route::get('/notices/public', [NoticeController::class, 'publicNotices'])->name('notices.public');
        
        // Reports
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/', [ReportController::class, 'index'])->name('index');
            Route::get('/students', [ReportController::class, 'studentsReport'])->name('students');
            Route::get('/teachers', [ReportController::class, 'teachersReport'])->name('teachers');
            Route::get('/exams', [ReportController::class, 'examsReport'])->name('exams');
            Route::get('/fees', [ReportController::class, 'feesReport'])->name('fees');
            Route::get('/attendance', [ReportController::class, 'attendanceReport'])->name('attendance');
            Route::get('/academic', [ReportController::class, 'academicReport'])->name('academic');
        });
        
        // Settings
        Route::get('/settings', [DashboardController::class, 'settings'])->name('settings');
        Route::post('/settings', [DashboardController::class, 'updateSettings'])->name('settings.update');
        
        // Profile
        Route::get('/profile', [DashboardController::class, 'profile'])->name('profile');
        Route::post('/profile', [DashboardController::class, 'updateProfile'])->name('profile.update');
    });
    
    // Teacher routes
    Route::prefix('teacher')->name('teacher.')->middleware('role:teacher')->group(function () {
        Route::get('/dashboard', [TeacherController::class, 'dashboard'])->name('dashboard');
        Route::get('/students', [TeacherController::class, 'students'])->name('students');
        Route::get('/routines', [TeacherController::class, 'routines'])->name('routines');
        Route::get('/notices', [TeacherController::class, 'notices'])->name('notices');
    });
    
    // Student routes
    Route::prefix('student')->name('student.')->middleware('role:student')->group(function () {
        Route::get('/dashboard', [StudentController::class, 'dashboard'])->name('dashboard');
        Route::get('/profile', [StudentController::class, 'studentProfile'])->name('profile');
        Route::get('/results', [StudentController::class, 'results'])->name('results');
        Route::get('/fees', [StudentController::class, 'fees'])->name('fees');
        Route::get('/routines', [StudentController::class, 'routines'])->name('routines');
        Route::get('/notices', [StudentController::class, 'notices'])->name('notices');
    });
});

// Public notice board
Route::get('/notices', [NoticeController::class, 'publicBoard'])->name('public.notices');
Route::get('/notices/{notice}', [NoticeController::class, 'publicShow'])->name('public.notices.show');

// API routes for AJAX requests
Route::prefix('api')->middleware(['auth'])->group(function () {
    Route::get('/students/search', [StudentController::class, 'search'])->name('api.students.search');
    Route::get('/teachers/search', [TeacherController::class, 'search'])->name('api.teachers.search');
    Route::get('/routines/check-conflict', [RoutineController::class, 'checkConflict'])->name('api.routines.check-conflict');
    Route::get('/fees/student-balance/{student}', [FeeController::class, 'studentBalance'])->name('api.fees.student-balance');
});
