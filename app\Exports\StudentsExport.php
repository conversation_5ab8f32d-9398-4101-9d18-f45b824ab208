<?php

namespace App\Exports;

use App\Models\Student;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StudentsExport implements FromQuery, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    protected $filters;

    public function __construct($filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Database\Query\Builder
     */
    public function query()
    {
        $query = Student::query();

        // Apply filters
        if (!empty($this->filters['session'])) {
            $query->where('session', $this->filters['session']);
        }

        if (!empty($this->filters['group'])) {
            $query->where('group', $this->filters['group']);
        }

        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        return $query->orderBy('roll');
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Roll',
            'Name',
            'Registration',
            'Group',
            'Father\'s Name',
            'Mother\'s Name',
            'Gender',
            'Date of Birth',
            'Session',
            'Subject 1',
            'Subject 2',
            'Subject 3',
            '4th Subject',
            'Phone',
            'Email',
            'Address',
            'Blood Group',
            'Religion',
            'Nationality',
            'Admission Date',
            'Previous School',
            'Previous GPA',
            'Status',
            'Guardian Phone',
            'Guardian Email',
            'Guardian Address',
            'Emergency Contact',
            'Medical Info',
            'Notes'
        ];
    }

    /**
     * @param Student $student
     * @return array
     */
    public function map($student): array
    {
        return [
            $student->roll,
            $student->name,
            $student->regi,
            $student->group,
            $student->fname,
            $student->mname,
            $student->gender,
            $student->dob ? $student->dob->format('Y-m-d') : '',
            $student->session,
            $student->sub1,
            $student->sub2,
            $student->sub3,
            $student->{'4th_sub'},
            $student->phone,
            $student->email,
            $student->address,
            $student->blood_group,
            $student->religion,
            $student->nationality,
            $student->admission_date ? $student->admission_date->format('Y-m-d') : '',
            $student->previous_school,
            $student->previous_gpa,
            $student->status,
            $student->guardian_phone,
            $student->guardian_email,
            $student->guardian_address,
            $student->emergency_contact,
            $student->medical_info,
            $student->notes,
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }
}
