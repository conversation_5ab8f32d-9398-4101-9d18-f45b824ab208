# SKUL Education Management System - Installation Guide

## Quick Setup for XAMPP Users

### Prerequisites
- XAMPP installed with PHP 7.4+ and MySQL
- Modern Web Browser (Chrome, Firefox, Safari, Edge)

### Step-by-Step Installation

#### 1. Download Project
```bash
# Download and extract the project files to your XAMPP htdocs folder
# Path should be: C:\xampp\htdocs\skul (Windows) or /opt/lampp/htdocs/skul (Linux)
```

#### 2. No Dependencies Required
This is a pure PHP project with no external dependencies to install.

#### 3. Environment Setup
```bash
# Copy environment file
copy .env.example .env

# Generate application key
php artisan key:generate
```

#### 4. Database Configuration
1. Start XAMPP Control Panel
2. Start Apache and MySQL services
3. Open phpMyAdmin (http://localhost/phpmyadmin)
4. Create a new database named `skul_education_db`
5. Edit `.env` file with your database credentials:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=skul_education_db
DB_USERNAME=root
DB_PASSWORD=
```

#### 5. Run Migrations and Seeders
```bash
php artisan migrate
php artisan db:seed
```

#### 6. Create Storage Link
```bash
php artisan storage:link
```

#### 7. Set Permissions (Linux/Mac only)
```bash
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

#### 8. Start Development Server
```bash
php artisan serve
```

The application will be available at: http://localhost:8000

### Default Login Credentials

#### Admin Access
- **URL**: http://localhost:8000/admin/dashboard
- **Email**: <EMAIL>
- **Password**: admin123

#### Teacher Access
- **URL**: http://localhost:8000/teacher/dashboard
- **Email**: <EMAIL>
- **Password**: teacher123

#### Student Access
- **URL**: http://localhost:8000/student/dashboard
- **Email**: <EMAIL>
- **Password**: student123

### Troubleshooting

#### Common Issues and Solutions

1. **Composer not found**
   - Download and install Composer from https://getcomposer.org/

2. **PHP version error**
   - Ensure PHP 8.1 or higher is installed
   - Check with: `php -v`

3. **Database connection error**
   - Verify MySQL is running in XAMPP
   - Check database name and credentials in `.env`

4. **Permission denied errors (Linux/Mac)**
   ```bash
   sudo chown -R www-data:www-data storage
   sudo chown -R www-data:www-data bootstrap/cache
   ```

5. **Storage link not working**
   ```bash
   php artisan storage:link --force
   ```

6. **Missing APP_KEY**
   ```bash
   php artisan key:generate
   ```

### Production Deployment

For production deployment:

1. Set `APP_ENV=production` in `.env`
2. Set `APP_DEBUG=false` in `.env`
3. Configure proper database credentials
4. Set up proper web server (Apache/Nginx)
5. Configure SSL certificate
6. Set up backup system
7. Configure email settings for notifications

### Additional Configuration

#### Email Configuration (Optional)
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your Institution Name"
```

#### Institution Settings
Edit `config/education.php` or use the admin settings panel to configure:
- Institution name and details
- Academic year settings
- Upload limits and file types
- Fee types and payment methods
- Exam grading system

### Next Steps

1. **Login as Admin**: Use the admin credentials to access the system
2. **Configure Institution**: Update institution details in settings
3. **Add Teachers**: Start adding teacher information
4. **Add Students**: Begin student enrollment
5. **Setup Academic Year**: Configure current academic session
6. **Customize Settings**: Adjust system settings as needed

### Support

If you encounter any issues:
1. Check the Laravel logs: `storage/logs/laravel.log`
2. Verify all requirements are met
3. Ensure proper file permissions
4. Check database connectivity

For additional help, create an issue in the project repository.

---

**Happy Learning with SKUL Education Management System!** 🎓
