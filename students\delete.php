<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: index.php?error=' . urlencode('ছাত্রের ID প্রদান করা হয়নি।'));
    exit();
}

$studentId = intval($_GET['id']);

// Get student details
$student = fetchRow("SELECT * FROM students WHERE id = ?", [$studentId]);

if (!$student) {
    header('Location: index.php?error=' . urlencode('ছাত্র পাওয়া যায়নি।'));
    exit();
}

$message = '';
$messageType = '';

// Handle deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    $result = deleteStudentSafely($studentId);
    
    if ($result['success']) {
        header('Location: index.php?success=' . urlencode($result['message']));
        exit();
    } else {
        $message = $result['message'];
        $messageType = 'error';
    }
}

function deleteStudentSafely($studentId) {
    try {
        // Get student details for cleanup
        $student = fetchRow("SELECT * FROM students WHERE id = ?", [$studentId]);

        if (!$student) {
            return ['success' => false, 'message' => 'ছাত্র পাওয়া যায়নি।'];
        }

        // Start transaction
        executeQuery("START TRANSACTION");

        // Delete related data first
        // Delete fee payments
        executeQuery("DELETE FROM fee_payments WHERE student_id = ?", [$studentId]);

        // Delete exam results
        executeQuery("DELETE FROM exam_results WHERE student_id = ?", [$studentId]);

        // Delete student picture if exists
        if (!empty($student['picture'])) {
            $picturePath = '../uploads/students/' . $student['picture'];
            if (file_exists($picturePath)) {
                unlink($picturePath);
            }
        }

        // Delete student record
        executeQuery("DELETE FROM students WHERE id = ?", [$studentId]);

        // Commit transaction
        executeQuery("COMMIT");

        return [
            'success' => true,
            'message' => "ছাত্র '{$student['name']}' এর তথ্য সফলভাবে মুছে ফেলা হয়েছে।"
        ];

    } catch (Exception $e) {
        // Rollback on error
        executeQuery("ROLLBACK");

        return [
            'success' => false,
            'message' => 'ছাত্রের তথ্য মুছতে সমস্যা হয়েছে: ' . $e->getMessage()
        ];
    }
}

// Get user data for navbar
$user = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র মুছুন - <?php echo htmlspecialchars($student['name']); ?> - SKUL</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .student-card {
            border: 2px solid #dc3545;
            border-radius: 10px;
            background: #fff5f5;
        }
        
        .student-photo {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 10px;
            border: 3px solid #dee2e6;
        }
        
        .danger-zone {
            background: #f8d7da;
            border: 2px solid #dc3545;
            border-radius: 8px;
            padding: 20px;
        }
        
        .warning-icon {
            font-size: 4rem;
            color: #dc3545;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .related-data {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="page-title text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                ছাত্র মুছে ফেলুন
                            </h2>
                            <p class="text-muted">সতর্কতা: এই কাজটি অপরিবর্তনীয়</p>
                        </div>
                        <div class="btn-group">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                            </a>
                            <a href="view.php?id=<?php echo $student['id']; ?>" class="btn btn-info">
                                <i class="fas fa-eye me-1"></i> বিস্তারিত দেখুন
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Student Information Card -->
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card student-card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-graduate me-2"></i>
                                ছাত্রের তথ্য
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <img src="<?php echo $student['picture'] ? '../uploads/students/' . $student['picture'] : '../assets/images/default-avatar.svg'; ?>"
                                         alt="<?php echo htmlspecialchars($student['name']); ?>" 
                                         class="student-photo">
                                </div>
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>নাম:</strong> <?php echo htmlspecialchars($student['name']); ?></p>
                                            <p><strong>রোল:</strong> <?php echo htmlspecialchars($student['roll'] ?: 'N/A'); ?></p>
                                            <p><strong>রেজিস্ট্রেশন:</strong> <?php echo htmlspecialchars($student['regi'] ?: 'N/A'); ?></p>
                                            <p><strong>গ্রুপ:</strong> <?php echo htmlspecialchars($student['group'] ?: 'N/A'); ?></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>সেশন:</strong> <?php echo htmlspecialchars($student['session'] ?: 'N/A'); ?></p>
                                            <p><strong>লিঙ্গ:</strong> <?php echo htmlspecialchars($student['gender'] ?: 'N/A'); ?></p>
                                            <p><strong>ফোন:</strong> <?php echo htmlspecialchars($student['phone'] ?: 'N/A'); ?></p>
                                            <p><strong>ইমেইল:</strong> <?php echo htmlspecialchars($student['email'] ?: 'N/A'); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warning and Related Data -->
            <div class="row mt-4">
                <div class="col-md-8 mx-auto">
                    <div class="danger-zone">
                        <div class="text-center mb-4">
                            <i class="fas fa-exclamation-triangle warning-icon"></i>
                            <h3 class="text-danger mt-3">সতর্কতা!</h3>
                            <p class="lead">আপনি <strong><?php echo htmlspecialchars($student['name']); ?></strong> এর সব তথ্য মুছে ফেলতে চলেছেন।</p>
                        </div>

                        <!-- Related Data Information -->
                        <div class="related-data">
                            <h6 class="text-warning mb-3">
                                <i class="fas fa-info-circle me-2"></i>যা মুছে যাবে:
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-danger me-2"></i>ব্যক্তিগত তথ্য</li>
                                        <li><i class="fas fa-check text-danger me-2"></i>প্রোফাইল ছবি</li>
                                        <li><i class="fas fa-check text-danger me-2"></i>যোগাযোগের তথ্য</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-danger me-2"></i>পরীক্ষার ফলাফল</li>
                                        <li><i class="fas fa-check text-danger me-2"></i>ফি পেমেন্ট রেকর্ড</li>
                                        <li><i class="fas fa-check text-danger me-2"></i>একাডেমিক ইতিহাস</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Confirmation Form -->
                        <form method="post" class="mt-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                <label class="form-check-label text-danger fw-bold" for="confirmDelete">
                                    হ্যাঁ, আমি নিশ্চিত যে আমি এই ছাত্রের সব তথ্য মুছে ফেলতে চাই।
                                </label>
                            </div>
                            
                            <div class="text-center">
                                <a href="index.php" class="btn btn-secondary btn-lg me-3">
                                    <i class="fas fa-times me-2"></i>বাতিল
                                </a>
                                <button type="submit" name="confirm_delete" class="btn btn-danger btn-lg" id="deleteBtn" disabled>
                                    <i class="fas fa-trash-alt me-2"></i>মুছে ফেলুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    
    <script>
        $(document).ready(function() {
            // Enable/disable delete button based on checkbox
            $('#confirmDelete').on('change', function() {
                $('#deleteBtn').prop('disabled', !this.checked);
            });
            
            // Final confirmation before deletion
            $('form').on('submit', function(e) {
                if (!$('#confirmDelete').is(':checked')) {
                    e.preventDefault();
                    alert('দয়া করে নিশ্চিতকরণ চেকবক্স টিক দিন।');
                    return false;
                }
                
                const studentName = '<?php echo addslashes($student['name']); ?>';
                const finalConfirm = confirm(`আপনি কি সত্যিই "${studentName}" এর সব তথ্য মুছে ফেলতে চান? এই কাজটি আর ফেরত নেওয়া যাবে না।`);
                
                if (!finalConfirm) {
                    e.preventDefault();
                    return false;
                }
                
                // Show loading state
                $('#deleteBtn').html('<i class="fas fa-spinner fa-spin me-2"></i>মুছে ফেলা হচ্ছে...');
                $('#deleteBtn').prop('disabled', true);
            });
        });
    </script>
</body>
</html>
