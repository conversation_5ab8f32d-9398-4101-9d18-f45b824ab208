<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Education System Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the education management system
    |
    */

    'institution' => [
        'name' => env('INSTITUTION_NAME', 'Your Institution Name'),
        'address' => env('INSTITUTION_ADDRESS', 'Your Institution Address'),
        'phone' => env('INSTITUTION_PHONE', 'Your Phone Number'),
        'email' => env('INSTITUTION_EMAIL', '<EMAIL>'),
        'website' => env('INSTITUTION_WEBSITE', 'www.yourinstitution.com'),
        'logo' => env('INSTITUTION_LOGO', 'images/logo.png'),
    ],

    'academic' => [
        'default_year' => env('DEFAULT_ACADEMIC_YEAR', date('Y')),
        'session_start_month' => 1, // January
        'session_end_month' => 12, // December
    ],

    'uploads' => [
        'max_size' => env('UPLOAD_MAX_SIZE', 10240), // KB
        'allowed_image_types' => explode(',', env('ALLOWED_IMAGE_TYPES', 'jpg,jpeg,png,gif')),
        'allowed_document_types' => explode(',', env('ALLOWED_DOCUMENT_TYPES', 'pdf,doc,docx,xls,xlsx')),
        'student_photo_path' => 'uploads/students/',
        'teacher_photo_path' => 'uploads/teachers/',
        'documents_path' => 'uploads/documents/',
    ],

    'student' => [
        'roll_prefix' => 'STU',
        'registration_prefix' => 'REG',
        'default_group' => 'Science',
        'groups' => [
            'Science',
            'Commerce',
            'Arts',
            'Vocational'
        ],
        'subjects' => [
            'Bangla',
            'English',
            'Mathematics',
            'Physics',
            'Chemistry',
            'Biology',
            'ICT',
            'Economics',
            'Accounting',
            'Management',
            'History',
            'Geography',
            'Civics',
            'Islamic Studies',
            'Moral Science'
        ],
        'genders' => [
            'Male',
            'Female',
            'Other'
        ]
    ],

    'teacher' => [
        'employee_prefix' => 'EMP',
        'designations' => [
            'Principal',
            'Vice Principal',
            'Assistant Principal',
            'Head Teacher',
            'Senior Teacher',
            'Assistant Teacher',
            'Junior Teacher',
            'Lecturer',
            'Assistant Professor',
            'Professor'
        ],
        'departments' => [
            'Science',
            'Commerce',
            'Arts',
            'Mathematics',
            'English',
            'Bangla',
            'Administration'
        ]
    ],

    'exam' => [
        'types' => [
            'First Terminal',
            'Second Terminal',
            'Final',
            'Test Exam',
            'Model Test',
            'Board Exam'
        ],
        'grades' => [
            'A+' => ['min' => 80, 'max' => 100, 'point' => 5.00],
            'A' => ['min' => 70, 'max' => 79, 'point' => 4.00],
            'A-' => ['min' => 60, 'max' => 69, 'point' => 3.50],
            'B' => ['min' => 50, 'max' => 59, 'point' => 3.00],
            'C' => ['min' => 40, 'max' => 49, 'point' => 2.00],
            'D' => ['min' => 33, 'max' => 39, 'point' => 1.00],
            'F' => ['min' => 0, 'max' => 32, 'point' => 0.00],
        ]
    ],

    'fee' => [
        'types' => [
            'Admission Fee',
            'Monthly Fee',
            'Exam Fee',
            'Library Fee',
            'Lab Fee',
            'Sports Fee',
            'Development Fee',
            'Transport Fee'
        ],
        'payment_methods' => [
            'Cash',
            'Bank Transfer',
            'Mobile Banking',
            'Online Payment'
        ]
    ],

    'committee' => [
        'types' => [
            'Academic Committee',
            'Disciplinary Committee',
            'Sports Committee',
            'Cultural Committee',
            'Examination Committee',
            'Finance Committee',
            'Development Committee'
        ]
    ],

    'notice' => [
        'types' => [
            'General',
            'Academic',
            'Examination',
            'Holiday',
            'Event',
            'Emergency',
            'Fee Collection'
        ],
        'priorities' => [
            'Low',
            'Medium',
            'High',
            'Urgent'
        ]
    ],

    'routine' => [
        'days' => [
            'Saturday',
            'Sunday',
            'Monday',
            'Tuesday',
            'Wednesday',
            'Thursday',
            'Friday'
        ],
        'periods' => [
            '1st Period' => '08:00-08:45',
            '2nd Period' => '08:45-09:30',
            '3rd Period' => '09:30-10:15',
            'Break' => '10:15-10:30',
            '4th Period' => '10:30-11:15',
            '5th Period' => '11:15-12:00',
            '6th Period' => '12:00-12:45',
            'Lunch Break' => '12:45-01:30',
            '7th Period' => '01:30-02:15',
            '8th Period' => '02:15-03:00'
        ]
    ]
];
