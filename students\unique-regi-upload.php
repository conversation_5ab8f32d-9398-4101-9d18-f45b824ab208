<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

// Increase limits for large files
ini_set('memory_limit', '1G');
ini_set('max_execution_time', 600);
set_time_limit(600);

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload') {
    $result = handleUniqueRegiUpload();
    $message = $result['message'];
    $messageType = $result['type'];
}

function handleUniqueRegiUpload() {
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        return ['message' => 'ফাইল আপলোড করতে সমস্যা হয়েছে।', 'type' => 'error'];
    }

    $file = $_FILES['csv_file'];
    $allowedTypes = ['text/csv', 'application/csv', 'text/plain'];
    $fileType = mime_content_type($file['tmp_name']);
    
    if (!in_array($fileType, $allowedTypes) && !str_ends_with($file['name'], '.csv')) {
        return ['message' => 'শুধুমাত্র CSV ফাইল আপলোড করুন।', 'type' => 'error'];
    }

    $maxSize = 50 * 1024 * 1024; // 50MB
    if ($file['size'] > $maxSize) {
        return ['message' => 'ফাইলের আকার ৫০ MB এর বেশি হতে পারবে না।', 'type' => 'error'];
    }

    return processUniqueRegiCSV($file['tmp_name']);
}

function processUniqueRegiCSV($filePath) {
    $handle = fopen($filePath, 'r');
    if (!$handle) {
        return ['message' => 'ফাইল পড়তে সমস্যা হয়েছে।', 'type' => 'error'];
    }

    // Check for BOM and remove it
    $bom = fread($handle, 3);
    if ($bom !== "\xEF\xBB\xBF") {
        rewind($handle);
    }

    // Read headers
    $headers = fgetcsv($handle);
    if (!$headers) {
        fclose($handle);
        return ['message' => 'CSV ফাইলে হেডার পাওয়া যায়নি।', 'type' => 'error'];
    }

    // Clean headers
    $headers = array_map(function($header) {
        return trim(preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $header));
    }, $headers);

    // Map headers (case insensitive)
    $headerMap = [];
    foreach ($headers as $index => $header) {
        $cleanHeader = trim(strtolower($header));
        $headerMap[$cleanHeader] = $index;
    }

    // Required fields
    $requiredFields = ['name', 'regi'];
    foreach ($requiredFields as $field) {
        if (!isset($headerMap[$field])) {
            fclose($handle);
            return ['message' => "প্রয়োজনীয় কলাম '$field' পাওয়া যায়নি।\nপাওয়া কলাম: " . implode(', ', array_keys($headerMap)), 'type' => 'error'];
        }
    }

    $successCount = 0;
    $updateCount = 0;
    $errorCount = 0;
    $skippedCount = 0;
    $errors = [];
    $rowNumber = 1;
    $debugInfo = [];

    try {
        executeQuery("START TRANSACTION");

        while (($row = fgetcsv($handle)) !== false) {
            $rowNumber++;
            
            // Skip empty rows
            if (empty(array_filter($row))) {
                $skippedCount++;
                continue;
            }

            // Map data to fields
            $studentData = [];
            foreach ($headerMap as $field => $index) {
                $value = isset($row[$index]) ? trim($row[$index]) : '';
                $studentData[$field] = $value;
            }

            // Debug first few rows
            if ($rowNumber <= 5) {
                $debugInfo[] = "Row $rowNumber: {$studentData['name']} (Regi: {$studentData['regi']})";
            }

            // Validate required fields
            if (empty($studentData['name'])) {
                $errorCount++;
                if ($errorCount <= 10) {
                    $errors[] = "সারি $rowNumber: নাম প্রয়োজন।";
                }
                continue;
            }

            if (empty($studentData['regi'])) {
                $errorCount++;
                if ($errorCount <= 10) {
                    $errors[] = "সারি $rowNumber: রেজিস্ট্রেশন নম্বর প্রয়োজন।";
                }
                continue;
            }

            // Set defaults and clean empty values
            $studentData['session'] = $studentData['session'] ?? date('Y');
            $studentData['status'] = 'Active';
            $studentData['nationality'] = 'Bangladeshi';

            // Convert empty strings to NULL for unique fields
            if (empty($studentData['email'])) {
                $studentData['email'] = null;
            }
            if (empty($studentData['phone'])) {
                $studentData['phone'] = null;
            }
            if (empty($studentData['roll'])) {
                $studentData['roll'] = null;
            }

            // Process student with unique registration logic
            $result = insertOrUpdateStudent($studentData, $rowNumber);
            
            if ($result['success']) {
                if ($result['updated']) {
                    $updateCount++;
                } else {
                    $successCount++;
                }
            } else {
                $errorCount++;
                if ($errorCount <= 10) {
                    $errors[] = $result['error'];
                }
            }

            // Progress tracking
            if ($rowNumber % 100 == 0) {
                $debugInfo[] = "Processed $rowNumber rows...";
            }
        }

        executeQuery("COMMIT");

    } catch (Exception $e) {
        executeQuery("ROLLBACK");
        fclose($handle);
        return ['message' => 'ডেটাবেস ত্রুটি: ' . $e->getMessage(), 'type' => 'error'];
    }

    fclose($handle);

    $totalProcessed = $successCount + $updateCount + $errorCount;
    $message = "আপলোড সম্পন্ন!\n";
    $message .= "মোট সারি: $rowNumber\n";
    $message .= "মোট প্রক্রিয়াকৃত: $totalProcessed\n";
    $message .= "নতুন যোগ: $successCount\n";
    $message .= "আপডেট: $updateCount\n";
    $message .= "ব্যর্থ: $errorCount\n";
    $message .= "খালি সারি এড়ানো: $skippedCount";

    if (!empty($debugInfo)) {
        $message .= "\n\nDebug Info:\n" . implode("\n", array_slice($debugInfo, 0, 10));
    }

    if (!empty($errors)) {
        $message .= "\n\nত্রুটি:\n" . implode("\n", $errors);
        if ($errorCount > 10) {
            $message .= "\n... এবং আরো " . ($errorCount - 10) . " টি ত্রুটি।";
        }
    }

    return [
        'message' => $message,
        'type' => ($successCount > 0 || $updateCount > 0) ? 'success' : 'warning'
    ];
}

function insertOrUpdateStudent($data, $rowNumber) {
    try {
        global $pdo;

        // Clean data - convert empty strings to NULL for unique fields
        $cleanData = [];
        foreach ($data as $key => $value) {
            if (in_array($key, ['email', 'phone', 'roll']) && empty($value)) {
                $cleanData[$key] = null;
            } else {
                $cleanData[$key] = $value;
            }
        }
        $data = $cleanData;

        // Check if student exists by registration number
        $existing = fetchRow("SELECT id FROM students WHERE regi = ?", [$data['regi']]);

        if ($existing) {
            // Update existing student
            $sql = "UPDATE students SET 
                    roll = ?, name = ?, `group` = ?, fname = ?, mname = ?, gender = ?, 
                    dob = ?, session = ?, sub1 = ?, sub2 = ?, sub3 = ?, `4th_sub` = ?, 
                    phone = ?, email = ?, address = ?, blood_group = ?, religion = ?, 
                    admission_date = ?, updated_at = NOW()
                    WHERE regi = ?";

            $params = [
                $data['roll'] ?? null, $data['name'], $data['group'] ?? null,
                $data['fname'] ?? null, $data['mname'] ?? null, $data['gender'] ?? null,
                formatStudentDate($data['dob'] ?? null), $data['session'], $data['sub1'] ?? null,
                $data['sub2'] ?? null, $data['sub3'] ?? null, $data['4th_sub'] ?? null,
                $data['phone'] ?? null, $data['email'] ?? null, $data['address'] ?? null,
                $data['blood_group'] ?? null, $data['religion'] ?? null,
                formatStudentDate($data['admission_date'] ?? null), $data['regi']
            ];

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);

            if ($result) {
                return ['success' => true, 'updated' => true];
            } else {
                $errorInfo = $stmt->errorInfo();
                return ['success' => false, 'error' => "সারি $rowNumber: Update failed - " . $errorInfo[2]];
            }

        } else {
            // Insert new student
            $sql = "INSERT INTO students (roll, name, regi, `group`, fname, mname, gender, dob, session, 
                    sub1, sub2, sub3, `4th_sub`, phone, email, address, blood_group, religion, 
                    nationality, admission_date, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $params = [
                $data['roll'] ?? null, $data['name'], $data['regi'], $data['group'] ?? null,
                $data['fname'] ?? null, $data['mname'] ?? null, $data['gender'] ?? null,
                formatStudentDate($data['dob'] ?? null), $data['session'], $data['sub1'] ?? null,
                $data['sub2'] ?? null, $data['sub3'] ?? null, $data['4th_sub'] ?? null,
                $data['phone'] ?? null, $data['email'] ?? null, $data['address'] ?? null,
                $data['blood_group'] ?? null, $data['religion'] ?? null, $data['nationality'],
                formatStudentDate($data['admission_date'] ?? null), $data['status']
            ];

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);

            if ($result && $stmt->rowCount() > 0) {
                return ['success' => true, 'updated' => false];
            } else {
                $errorInfo = $stmt->errorInfo();
                return ['success' => false, 'error' => "সারি $rowNumber: Insert failed - " . $errorInfo[2]];
            }
        }

    } catch (PDOException $e) {
        return ['success' => false, 'error' => "সারি $rowNumber: PDO Error - " . $e->getMessage()];
    } catch (Exception $e) {
        return ['success' => false, 'error' => "সারি $rowNumber: Error - " . $e->getMessage()];
    }
}

function formatStudentDate($dateString) {
    if (empty($dateString)) {
        return null;
    }

    $date = date('Y-m-d', strtotime($dateString));
    return ($date && $date !== '1970-01-01') ? $date : null;
}

// Get user data for navbar
$user = getCurrentUser();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unique Registration Upload - Students</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <?php include '../includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-id-card me-2"></i>Unique Registration Upload
                    </h1>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : ($messageType === 'warning' ? 'warning' : 'success'); ?> alert-dismissible fade show">
                        <pre style="white-space: pre-wrap; margin: 0;"><?php echo htmlspecialchars($message); ?></pre>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-csv me-2"></i>CSV ফাইল আপলোড করুন
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6 class="text-info mb-2">
                                        <i class="fas fa-info-circle me-2"></i>বিশেষ বৈশিষ্ট্য:
                                    </h6>
                                    <ul class="mb-0">
                                        <li><strong>Unique Registration:</strong> রেজিস্ট্রেশন নম্বর অনুসারে ইউনিক আপলোড</li>
                                        <li><strong>Insert or Update:</strong> নতুন হলে যোগ, পুরাতন হলে আপডেট</li>
                                        <li><strong>Required Fields:</strong> Name এবং Regi অবশ্যই থাকতে হবে</li>
                                    </ul>
                                </div>

                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="upload">
                                    
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">CSV ফাইল নির্বাচন করুন</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                        <div class="form-text">সর্বোচ্চ ফাইল সাইজ: ৫০ MB</div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-upload me-2"></i>আপলোড করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>প্রয়োজনীয় কলাম
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <strong>Required:</strong>
                                        <ul class="list-unstyled text-danger">
                                            <li>• Name</li>
                                            <li>• Regi</li>
                                        </ul>
                                    </div>
                                    <div class="col-6">
                                        <strong>Optional:</strong>
                                        <ul class="list-unstyled text-muted small">
                                            <li>• Roll</li>
                                            <li>• Group</li>
                                            <li>• Fname</li>
                                            <li>• Mname</li>
                                            <li>• Gender</li>
                                            <li>• DOB</li>
                                            <li>• Session</li>
                                            <li>• Sub1, Sub2, Sub3</li>
                                            <li>• 4th_Sub</li>
                                            <li>• Phone</li>
                                            <li>• Email</li>
                                            <li>• Address</li>
                                            <li>• Blood_Group</li>
                                            <li>• Religion</li>
                                            <li>• Admission_Date</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>Features
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Registration-based uniqueness</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Insert new students</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Update existing students</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Batch processing</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Detailed reporting</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Error handling</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <a href="index.php" class="btn btn-outline-primary">
                                <i class="fas fa-list me-2"></i>ছাত্রদের তালিকা
                            </a>
                            <a href="csv-analyzer.php" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-search me-2"></i>CSV Analyzer
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
