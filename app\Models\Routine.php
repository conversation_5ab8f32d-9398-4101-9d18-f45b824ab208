<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Routine extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'session',
        'group',
        'day',
        'period',
        'start_time',
        'end_time',
        'subject',
        'teacher_id',
        'room',
        'notes',
        'status'
    ];

    protected $casts = [
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i'
    ];

    // Relationships
    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeBySession($query, $session)
    {
        return $query->where('session', $session);
    }

    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    public function scopeByDay($query, $day)
    {
        return $query->where('day', $day);
    }

    public function scopeClassRoutine($query)
    {
        return $query->where('type', 'Class Routine');
    }

    public function scopeExamRoutine($query)
    {
        return $query->where('type', 'Exam Routine');
    }

    // Helper methods
    public function getDuration()
    {
        return $this->start_time->diffInMinutes($this->end_time);
    }

    public function isConflicting($teacherId, $day, $startTime, $endTime, $excludeId = null)
    {
        $query = static::where('teacher_id', $teacherId)
                      ->where('day', $day)
                      ->where('status', 'Active')
                      ->where(function ($q) use ($startTime, $endTime) {
                          $q->whereBetween('start_time', [$startTime, $endTime])
                            ->orWhereBetween('end_time', [$startTime, $endTime])
                            ->orWhere(function ($subQ) use ($startTime, $endTime) {
                                $subQ->where('start_time', '<=', $startTime)
                                     ->where('end_time', '>=', $endTime);
                            });
                      });

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    public function getFormattedTimeAttribute()
    {
        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }
}
