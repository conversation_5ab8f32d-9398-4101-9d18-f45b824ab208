<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Exam extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'session',
        'group',
        'start_date',
        'end_date',
        'description',
        'status'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date'
    ];

    // Relationships
    public function examResults()
    {
        return $this->hasMany(ExamResult::class);
    }

    // Scopes
    public function scopeBySession($query, $session)
    {
        return $query->where('session', $session);
    }

    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeActive($query)
    {
        return $query->where('status', '!=', 'Cancelled');
    }

    // Helper methods
    public function getParticipantCount()
    {
        return $this->examResults()->distinct('student_id')->count();
    }

    public function getAverageMarks($subject = null)
    {
        $query = $this->examResults();
        
        if ($subject) {
            $query->where('subject', $subject);
        }
        
        return $query->avg('marks_obtained');
    }

    public function getPassPercentage($passingMarks = 33)
    {
        $totalStudents = $this->getParticipantCount();
        
        if ($totalStudents == 0) {
            return 0;
        }
        
        $passedStudents = $this->examResults()
                              ->where('marks_obtained', '>=', $passingMarks)
                              ->distinct('student_id')
                              ->count();
        
        return ($passedStudents / $totalStudents) * 100;
    }
}
