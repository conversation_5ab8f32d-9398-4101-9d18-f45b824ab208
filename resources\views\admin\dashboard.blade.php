@extends('layouts.admin')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">Welcome back, {{ auth()->user()->name }}!</h3>
                        <p class="mb-0">Here's what's happening at {{ config('education.institution.name', 'your institution') }} today.</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <i class="fas fa-tachometer-alt fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-white-50 mb-0">Total Students</h5>
                        <span class="h2 font-weight-bold mb-0 text-white">{{ number_format($stats['total_students']) }}</span>
                        <p class="mb-0 text-white-50">
                            <span class="text-success me-2">{{ $stats['active_students'] }}</span>
                            Active
                        </p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-graduate stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-white-50 mb-0">Total Teachers</h5>
                        <span class="h2 font-weight-bold mb-0 text-white">{{ number_format($stats['total_teachers']) }}</span>
                        <p class="mb-0 text-white-50">
                            <span class="text-success me-2">{{ $stats['active_teachers'] }}</span>
                            Active
                        </p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chalkboard-teacher stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-white-50 mb-0">Total Exams</h5>
                        <span class="h2 font-weight-bold mb-0 text-white">{{ number_format($stats['total_exams']) }}</span>
                        <p class="mb-0 text-white-50">
                            <span class="text-warning me-2">{{ $stats['upcoming_exams'] }}</span>
                            Upcoming
                        </p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-list stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-white-50 mb-0">This Month</h5>
                        <span class="h2 font-weight-bold mb-0 text-white">৳{{ number_format($feeStats['total_collected_this_month']) }}</span>
                        <p class="mb-0 text-white-50">Fee Collection</p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave stats-icon text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Monthly Fee Collection ({{ date('Y') }})
                </h5>
            </div>
            <div class="card-body">
                <canvas id="feeCollectionChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Students by Group
                </h5>
            </div>
            <div class="card-body">
                <canvas id="studentsGroupChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Recent Students
                </h5>
                <a href="{{ route('admin.students.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                @if($recentStudents->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recentStudents as $student)
                            <div class="list-group-item d-flex align-items-center px-0">
                                <img src="{{ $student->picture_url }}" alt="{{ $student->name }}" class="avatar me-3">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $student->name }}</h6>
                                    <small class="text-muted">{{ $student->roll }} • {{ $student->group }}</small>
                                </div>
                                <span class="badge bg-{{ $student->status === 'Active' ? 'success' : 'secondary' }}">
                                    {{ $student->status }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted text-center py-3">No recent students</p>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bullhorn me-2"></i>
                    Recent Notices
                </h5>
                <a href="{{ route('admin.notices.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                @if($recentNotices->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recentNotices as $notice)
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ Str::limit($notice->title, 40) }}</h6>
                                        <small class="text-muted">
                                            {{ $notice->publish_date->format('M d, Y') }} • 
                                            {{ $notice->creator->name }}
                                        </small>
                                    </div>
                                    <span class="badge bg-{{ $notice->priority_badge }}">
                                        {{ $notice->priority }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted text-center py-3">No recent notices</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Upcoming Exams -->
@if($upcomingExams->count() > 0)
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    Upcoming Exams
                </h5>
                <a href="{{ route('admin.exams.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($upcomingExams as $exam)
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-start border-primary border-4">
                                <div class="card-body">
                                    <h6 class="card-title">{{ $exam->name }}</h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ $exam->start_date->format('M d, Y') }}
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-layer-group me-1"></i>
                                            {{ $exam->group ?? 'All Groups' }}
                                        </small>
                                    </p>
                                    <span class="badge bg-{{ $exam->status === 'Scheduled' ? 'warning' : 'info' }}">
                                        {{ $exam->status }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Fee Collection Chart
    const feeCtx = document.getElementById('feeCollectionChart').getContext('2d');
    new Chart(feeCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Fee Collection (৳)',
                data: @json($monthlyCollections),
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '৳' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Students by Group Chart
    const groupCtx = document.getElementById('studentsGroupChart').getContext('2d');
    new Chart(groupCtx, {
        type: 'doughnut',
        data: {
            labels: @json(array_keys($studentsByGroup)),
            datasets: [{
                data: @json(array_values($studentsByGroup)),
                backgroundColor: [
                    '#667eea',
                    '#764ba2',
                    '#f093fb',
                    '#f5576c',
                    '#4facfe',
                    '#43e97b'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
</script>
@endpush
