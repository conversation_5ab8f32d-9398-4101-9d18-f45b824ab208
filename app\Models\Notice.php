<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notice extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'type',
        'priority',
        'publish_date',
        'expiry_date',
        'target_audience',
        'attachment',
        'created_by',
        'is_published',
        'send_notification',
        'status'
    ];

    protected $casts = [
        'publish_date' => 'date',
        'expiry_date' => 'date',
        'is_published' => 'boolean',
        'send_notification' => 'boolean'
    ];

    // Relationships
    public function creator()
    {
        return $this->belongsTo(Teacher::class, 'created_by');
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('is_published', true)
                    ->where('status', 'Published');
    }

    public function scopeActive($query)
    {
        return $query->where('status', '!=', 'Archived')
                    ->where(function ($q) {
                        $q->whereNull('expiry_date')
                          ->orWhere('expiry_date', '>=', now()->toDateString());
                    });
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeForAudience($query, $audience)
    {
        return $query->where('target_audience', $audience)
                    ->orWhere('target_audience', 'All');
    }

    // Accessors
    public function getAttachmentUrlAttribute()
    {
        if ($this->attachment) {
            return asset('storage/' . $this->attachment);
        }
        return null;
    }

    public function getIsExpiredAttribute()
    {
        if ($this->expiry_date) {
            return $this->expiry_date < now()->toDateString();
        }
        return false;
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'Draft' => 'secondary',
            'Published' => 'success',
            'Expired' => 'warning',
            'Archived' => 'dark'
        ];
        
        return $badges[$this->status] ?? 'secondary';
    }

    public function getPriorityBadgeAttribute()
    {
        $badges = [
            'Low' => 'info',
            'Medium' => 'warning',
            'High' => 'danger',
            'Urgent' => 'danger'
        ];
        
        return $badges[$this->priority] ?? 'info';
    }

    // Helper methods
    public function publish()
    {
        $this->update([
            'is_published' => true,
            'status' => 'Published',
            'publish_date' => now()->toDateString()
        ]);
    }

    public function archive()
    {
        $this->update(['status' => 'Archived']);
    }

    public function checkExpiry()
    {
        if ($this->expiry_date && $this->expiry_date < now()->toDateString()) {
            $this->update(['status' => 'Expired']);
        }
    }
}
