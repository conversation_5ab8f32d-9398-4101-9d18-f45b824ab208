<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

// Increase memory and time limits for large CSV files
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 300); // 5 minutes
set_time_limit(300);

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'upload':
                $result = handleBulkUpload();
                $message = $result['message'];
                $messageType = $result['type'];
                break;
            case 'download_template':
                downloadTemplate();
                break;
            case 'reset_all_students':
                $result = resetAllStudents();
                $message = $result['message'];
                $messageType = $result['type'];
                break;
            case 'check_constraints':
                $result = checkDatabaseConstraints();
                $message = $result['message'];
                $messageType = $result['type'];
                break;
        }
    }
}

function handleBulkUpload() {
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        return ['message' => 'ফাইল আপলোড করতে সমস্যা হয়েছে।', 'type' => 'error'];
    }

    $file = $_FILES['csv_file'];
    $allowedTypes = ['text/csv', 'application/csv', 'text/plain'];
    $fileType = mime_content_type($file['tmp_name']);
    
    if (!in_array($fileType, $allowedTypes) && !str_ends_with($file['name'], '.csv')) {
        return ['message' => 'শুধুমাত্র CSV ফাইল আপলোড করুন।', 'type' => 'error'];
    }

    $maxSize = 10 * 1024 * 1024; // 10MB
    if ($file['size'] > $maxSize) {
        return ['message' => 'ফাইলের আকার ১০ MB এর বেশি হতে পারবে না।', 'type' => 'error'];
    }

    return processCSVFile($file['tmp_name']);
}

function processCSVFile($filePath) {
    $handle = fopen($filePath, 'r');
    if (!$handle) {
        return ['message' => 'ফাইল পড়তে সমস্যা হয়েছে।', 'type' => 'error'];
    }

    $headers = fgetcsv($handle);
    if (!$headers) {
        fclose($handle);
        return ['message' => 'CSV ফাইলে হেডার পাওয়া যায়নি।', 'type' => 'error'];
    }

    // Validate headers
    $requiredHeaders = ['name'];

    $headerMap = [];
    foreach ($headers as $index => $header) {
        $headerMap[trim(strtolower($header))] = $index;
    }

    // Check required headers
    foreach ($requiredHeaders as $required) {
        if (!isset($headerMap[$required])) {
            fclose($handle);
            return ['message' => "প্রয়োজনীয় কলাম '$required' পাওয়া যায়নি।", 'type' => 'error'];
        }
    }

    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    $rowNumber = 1;
    $batchSize = 100; // Process in batches of 100
    $batchCount = 0;

    // Start transaction for batch processing
    try {
        executeQuery("START TRANSACTION");

        while (($row = fgetcsv($handle)) !== false) {
            $rowNumber++;

            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }

            $studentData = [];

            // Map CSV data to student fields
            foreach ($headerMap as $field => $index) {
                $value = isset($row[$index]) ? trim($row[$index]) : '';
                $studentData[$field] = $value;
            }

            // Validate required fields
            if (empty($studentData['name'])) {
                $errors[] = "সারি $rowNumber: ছাত্রের নাম প্রয়োজন।";
                $errorCount++;
                continue;
            }

            // Generate registration if not provided (roll will be kept as provided in CSV)
            if (empty($studentData['regi'])) {
                $studentData['regi'] = generateRegistration();
            }

            // Set default values
            $studentData['session'] = $studentData['session'] ?: date('Y');
            $studentData['status'] = 'Active';
            $studentData['nationality'] = 'Bangladeshi';

            // Validate and insert student
            $result = insertStudent($studentData, $rowNumber);
            if ($result['success']) {
                $successCount++;
                $batchCount++;

                // Commit batch every 100 records
                if ($batchCount >= $batchSize) {
                    executeQuery("COMMIT");
                    executeQuery("START TRANSACTION");
                    $batchCount = 0;
                }
            } else {
                $errorCount++;
                $errors[] = $result['error'];

                // Stop processing if too many errors
                if ($errorCount > 50) {
                    $errors[] = "অনেক বেশি ত্রুটি। প্রক্রিয়া বন্ধ করা হয়েছে।";
                    break;
                }
            }

            // Memory cleanup every 500 rows
            if ($rowNumber % 500 == 0) {
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
        }

        // Commit remaining records
        executeQuery("COMMIT");

    } catch (Exception $e) {
        executeQuery("ROLLBACK");
        fclose($handle);
        return ['message' => 'ডেটাবেস ত্রুটি: ' . $e->getMessage(), 'type' => 'error'];
    }

    fclose($handle);

    $message = "আপলোড সম্পন্ন! সফল: $successCount, ব্যর্থ: $errorCount";
    if (!empty($errors)) {
        $message .= "\n\nত্রুটি:\n" . implode("\n", array_slice($errors, 0, 10));
        if (count($errors) > 10) {
            $message .= "\n... এবং আরো " . (count($errors) - 10) . " টি ত্রুটি।";
        }
    }

    return [
        'message' => $message,
        'type' => $successCount > 0 ? 'success' : 'error'
    ];
}

function insertStudent($data, $rowNumber) {
    try {
        // Skip duplicate checks for faster processing - use INSERT IGNORE instead
        // Only validate email format if provided
        if (!empty($data['email'])) {
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                return ['success' => false, 'error' => "সারি $rowNumber: ইমেইল ঠিকানা সঠিক নয়।"];
            }
        }

        // Validate date fields
        if (!empty($data['dob'])) {
            $dob = date('Y-m-d', strtotime($data['dob']));
            if (!$dob || $dob === '1970-01-01') {
                return ['success' => false, 'error' => "সারি $rowNumber: জন্ম তারিখ সঠিক নয়।"];
            }
            $data['dob'] = $dob;
        }

        if (!empty($data['admission_date'])) {
            $admissionDate = date('Y-m-d', strtotime($data['admission_date']));
            if (!$admissionDate || $admissionDate === '1970-01-01') {
                return ['success' => false, 'error' => "সারি $rowNumber: ভর্তির তারিখ সঠিক নয়।"];
            }
            $data['admission_date'] = $admissionDate;
        }

        // Validate blood group
        $validBloodGroups = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
        if (!empty($data['blood_group']) && !in_array($data['blood_group'], $validBloodGroups)) {
            return ['success' => false, 'error' => "সারি $rowNumber: রক্তের গ্রুপ সঠিক নয়।"];
        }

        // Validate gender
        $validGenders = ['Male', 'Female', 'Other'];
        if (!empty($data['gender']) && !in_array($data['gender'], $validGenders)) {
            return ['success' => false, 'error' => "সারি $rowNumber: লিঙ্গ সঠিক নয়।"];
        }

        // Insert student using INSERT IGNORE to skip duplicates
        $sql = "INSERT IGNORE INTO students (roll, name, regi, `group`, fname, mname, gender, dob, session,
                sub1, sub2, sub3, `4th_sub`, phone, email, address, blood_group, religion,
                nationality, admission_date, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $params = [
            $data['roll'], $data['name'], $data['regi'], $data['group'] ?? null,
            $data['fname'] ?? null, $data['mname'] ?? null, $data['gender'] ?? null,
            $data['dob'] ?? null, $data['session'], $data['sub1'] ?? null,
            $data['sub2'] ?? null, $data['sub3'] ?? null, $data['4th_sub'] ?? null,
            $data['phone'] ?? null, $data['email'] ?? null, $data['address'] ?? null,
            $data['blood_group'] ?? null, $data['religion'] ?? null, $data['nationality'],
            $data['admission_date'] ?? null, $data['status']
        ];

        // Use direct PDO for better error handling
        global $pdo;
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($params);

        if ($result && $stmt->rowCount() > 0) {
            return ['success' => true];
        } else {
            // INSERT IGNORE may skip due to duplicates
            return ['success' => true]; // Consider skipped as success for bulk upload
        }

    } catch (PDOException $e) {
        return ['success' => false, 'error' => "সারি $rowNumber: PDO Error - " . $e->getMessage()];
    } catch (Exception $e) {
        return ['success' => false, 'error' => "সারি $rowNumber: " . $e->getMessage()];
    }
}

function downloadTemplate() {
    $headers = [
        'roll', 'name', 'regi', 'group', 'fname', 'mname', 'gender', 
        'dob', 'session', 'sub1', 'sub2', 'sub3', '4th_sub', 'phone', 
        'email', 'address', 'blood_group', 'religion', 'admission_date'
    ];

    $filename = 'student_import_template.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    fputcsv($output, $headers);
    
    // Add sample data with different groups having same roll numbers
    fputcsv($output, [
        '1', 'জন ডো', 'REG2025001', 'Science', 'বাবার নাম',
        'মায়ের নাম', 'Male', '2005-01-15', '2025', 'Physics',
        'Chemistry', 'Mathematics', 'Biology', '***********',
        '<EMAIL>', 'নমুনা ঠিকানা', 'A+', 'Islam', '2025-01-01'
    ]);

    fputcsv($output, [
        '1', 'জেন ডো', 'REG2025002', 'Commerce', 'বাবার নাম',
        'মায়ের নাম', 'Female', '2005-02-20', '2025', 'Accounting',
        'Economics', 'Business Studies', 'Mathematics', '***********',
        '<EMAIL>', 'নমুনা ঠিকানা ২', 'B+', 'Islam', '2025-01-01'
    ]);

    fputcsv($output, [
        '1', 'আলী হাসান', 'REG2025003', 'Arts', 'বাবার নাম',
        'মায়ের নাম', 'Male', '2005-03-10', '2025', 'Bangla',
        'English', 'History', 'Geography', '***********',
        '<EMAIL>', 'নমুনা ঠিকানা ৩', 'O+', 'Islam', '2025-01-01'
    ]);
    
    fclose($output);
    exit();
}

function resetAllStudents() {
    try {
        // Get total count before deletion
        $totalCount = fetchRow("SELECT COUNT(*) as total FROM students")['total'];

        if ($totalCount == 0) {
            return ['message' => 'কোন ছাত্র-ছাত্রী পাওয়া যায়নি।', 'type' => 'warning'];
        }

        // Start transaction for data integrity
        executeQuery("START TRANSACTION");

        // Delete all student pictures first
        $students = fetchAll("SELECT picture FROM students WHERE picture IS NOT NULL AND picture != ''");
        foreach ($students as $student) {
            $picturePath = '../uploads/students/' . $student['picture'];
            if (file_exists($picturePath)) {
                unlink($picturePath);
            }
        }

        // Get all student IDs first
        $studentIds = fetchAll("SELECT id FROM students");

        // Delete related data for each student (safer approach)
        foreach ($studentIds as $student) {
            $studentId = $student['id'];

            // Delete fee payments for this student
            executeQuery("DELETE FROM fee_payments WHERE student_id = ?", [$studentId]);

            // Delete exam results for this student
            executeQuery("DELETE FROM exam_results WHERE student_id = ?", [$studentId]);
        }

        // Now delete all students
        executeQuery("DELETE FROM students");

        // Reset auto increment
        executeQuery("ALTER TABLE students AUTO_INCREMENT = 1");

        // Commit transaction
        executeQuery("COMMIT");

        return [
            'message' => "সফলভাবে $totalCount জন ছাত্র-ছাত্রীর তথ্য এবং সংশ্লিষ্ট সব ডেটা মুছে ফেলা হয়েছে।",
            'type' => 'success'
        ];

    } catch (Exception $e) {
        // Rollback transaction on error
        executeQuery("ROLLBACK");
        executeQuery("SET FOREIGN_KEY_CHECKS = 1"); // Ensure foreign key checks are re-enabled

        return [
            'message' => 'ছাত্র-ছাত্রীর তথ্য মুছতে সমস্যা হয়েছে: ' . $e->getMessage(),
            'type' => 'error'
        ];
    }
}

function checkDatabaseConstraints() {
    try {
        $info = [];

        // Check students count
        $studentCount = fetchRow("SELECT COUNT(*) as total FROM students")['total'];
        $info[] = "ছাত্র-ছাত্রী: $studentCount জন";

        // Check fee payments
        $feePayments = fetchRow("SELECT COUNT(*) as total FROM fee_payments")['total'];
        $info[] = "ফি পেমেন্ট: $feePayments টি";

        // Check exam results
        $examResults = fetchRow("SELECT COUNT(*) as total FROM exam_results")['total'];
        $info[] = "পরীক্ষার ফলাফল: $examResults টি";

        // Check foreign key constraints
        $constraints = fetchAll("
            SELECT
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM
                INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE
                REFERENCED_TABLE_SCHEMA = DATABASE()
                AND REFERENCED_TABLE_NAME = 'students'
        ");

        $constraintInfo = [];
        foreach ($constraints as $constraint) {
            $constraintInfo[] = $constraint['TABLE_NAME'] . '.' . $constraint['COLUMN_NAME'] . ' -> students.' . $constraint['REFERENCED_COLUMN_NAME'];
        }

        $message = "ডেটাবেস তথ্য:\n" . implode("\n", $info);
        if (!empty($constraintInfo)) {
            $message .= "\n\nForeign Key Constraints:\n" . implode("\n", $constraintInfo);
        }

        return [
            'message' => $message,
            'type' => 'info'
        ];

    } catch (Exception $e) {
        return [
            'message' => 'ডেটাবেস চেক করতে সমস্যা: ' . $e->getMessage(),
            'type' => 'error'
        ];
    }
}

// Get user data for navbar
$user = getCurrentUser();

// Get current student count
$studentCount = fetchRow("SELECT COUNT(*) as total FROM students")['total'];
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বাল্ক আপলোড - ছাত্র-ছাত্রী - SKUL</title>

    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">

    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .upload-area.dragover {
            border-color: #28a745;
            background: #d4edda;
        }

        .file-info {
            background: #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
        }

        .progress-container {
            display: none;
            margin-top: 20px;
        }

        .auto-complete-container {
            position: relative;
        }

        .auto-complete-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .auto-complete-item {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }

        .auto-complete-item:hover {
            background: #f8f9fa;
        }

        .auto-complete-item.active {
            background: #007bff;
            color: white;
        }

        .field-mapping {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .mapping-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .csv-column {
            flex: 1;
            font-weight: bold;
            color: #495057;
        }

        .mapping-arrow {
            margin: 0 15px;
            color: #6c757d;
        }

        .db-field {
            flex: 1;
        }

        .validation-status {
            width: 30px;
            text-align: center;
        }

        .status-icon {
            font-size: 18px;
        }

        .status-valid {
            color: #28a745;
        }

        .status-invalid {
            color: #dc3545;
        }

        .status-warning {
            color: #ffc107;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="page-title">
                                <i class="fas fa-upload me-2"></i>
                                বাল্ক আপলোড - ছাত্র-ছাত্রী
                            </h2>
                            <p class="text-muted">CSV ফাইল ব্যবহার করে একসাথে অনেক ছাত্র-ছাত্রীর তথ্য আপলোড করুন</p>
                            <div class="mt-2">
                                <span class="badge bg-info fs-6">
                                    <i class="fas fa-users me-1"></i>
                                    বর্তমানে <?php echo $studentCount; ?> জন ছাত্র-ছাত্রী রয়েছে
                                </span>
                            </div>
                        </div>
                        <div class="btn-group">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                            </a>
                            <form method="post" style="display: inline;">
                                <input type="hidden" name="action" value="download_template">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-download me-1"></i> টেমপ্লেট ডাউনলোড
                                </button>
                            </form>
                            <form method="post" style="display: inline;">
                                <input type="hidden" name="action" value="check_constraints">
                                <button type="submit" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-info-circle me-1"></i> ডেটাবেস চেক
                                </button>
                            </form>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#resetModal"
                                    <?php echo $studentCount == 0 ? 'disabled title="কোন ছাত্র-ছাত্রী নেই"' : ''; ?>>
                                <i class="fas fa-trash-alt me-1"></i> সব ছাত্র মুছুন
                                <?php if ($studentCount > 0): ?>
                                    <span class="badge bg-light text-dark ms-1"><?php echo $studentCount; ?></span>
                                <?php endif; ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                    <?php echo nl2br(htmlspecialchars($message)); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Instructions Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        নির্দেশনা
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-file-csv me-2"></i>CSV ফাইল প্রস্তুতি:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>প্রথমে টেমপ্লেট ডাউনলোড করুন</li>
                                <li><i class="fas fa-check text-success me-2"></i>প্রয়োজনীয় তথ্য পূরণ করুন</li>
                                <li><i class="fas fa-check text-success me-2"></i>CSV ফরম্যাটে সেভ করুন</li>
                                <li><i class="fas fa-check text-success me-2"></i>UTF-8 এনকোডিং ব্যবহার করুন</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>গুরুত্বপূর্ণ:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-info text-info me-2"></i>সর্বোচ্চ ফাইল সাইজ: ১০ MB</li>
                                <li><i class="fas fa-info text-info me-2"></i>শুধুমাত্র CSV ফাইল গ্রহণযোগ্য</li>
                                <li><i class="fas fa-info text-info me-2"></i>নাম ফিল্ড অবশ্যই পূরণ করতে হবে</li>
                                <li><i class="fas fa-info text-info me-2"></i>রেজিস্ট্রেশন খালি থাকলে অটো জেনারেট হবে</li>
                                <li><i class="fas fa-info text-info me-2"></i>একই রোল বিভিন্ন গ্রুপে থাকতে পারে</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cloud-upload-alt me-2"></i>
                        ফাইল আপলোড
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="uploadForm">
                        <input type="hidden" name="action" value="upload">

                        <!-- File Upload Area -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>CSV ফাইল এখানে ড্রাগ করুন অথবা ক্লিক করে নির্বাচন করুন</h5>
                                <p class="text-muted">সর্বোচ্চ ফাইল সাইজ: ১০ MB</p>
                                <input type="file" name="csv_file" id="csvFile" accept=".csv" class="d-none" required>
                                <button type="button" class="btn btn-primary" onclick="document.getElementById('csvFile').click()">
                                    <i class="fas fa-folder-open me-2"></i>ফাইল নির্বাচন করুন
                                </button>
                            </div>
                        </div>

                        <!-- File Info -->
                        <div id="fileInfo" class="file-info" style="display: none;">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <i class="fas fa-file-csv text-success me-2"></i>
                                    <span id="fileName"></span>
                                    <small class="text-muted ms-2">(<span id="fileSize"></span>)</small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile()">
                                        <i class="fas fa-times me-1"></i>সরান
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="progress-container" id="progressContainer">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%" id="progressBar">
                                    <span id="progressText">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Field Mapping Preview -->
                        <div id="fieldMapping" class="field-mapping" style="display: none;">
                            <h6><i class="fas fa-exchange-alt me-2"></i>ফিল্ড ম্যাপিং প্রিভিউ</h6>
                            <div id="mappingContent">
                                <!-- Dynamic content will be loaded here -->
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-success btn-lg" id="submitBtn" disabled>
                                <i class="fas fa-upload me-2"></i>আপলোড শুরু করুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sample Data Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>
                        নমুনা ডেটা ফরম্যাট
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>roll</th>
                                    <th>name</th>
                                    <th>regi</th>
                                    <th>group</th>
                                    <th>fname</th>
                                    <th>mname</th>
                                    <th>gender</th>
                                    <th>dob</th>
                                    <th>session</th>
                                    <th>phone</th>
                                    <th>email</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><small>1</small></td>
                                    <td><small>জন ডো</small></td>
                                    <td><small>REG2025001</small></td>
                                    <td><small>Science</small></td>
                                    <td><small>বাবার নাম</small></td>
                                    <td><small>মায়ের নাম</small></td>
                                    <td><small>Male</small></td>
                                    <td><small>2005-01-15</small></td>
                                    <td><small>2025</small></td>
                                    <td><small>***********</small></td>
                                    <td><small><EMAIL></small></td>
                                </tr>
                                <tr>
                                    <td><small>1</small></td>
                                    <td><small>জেন ডো</small></td>
                                    <td><small>REG2025002</small></td>
                                    <td><small>Commerce</small></td>
                                    <td><small>বাবার নাম</small></td>
                                    <td><small>মায়ের নাম</small></td>
                                    <td><small>Female</small></td>
                                    <td><small>2005-02-20</small></td>
                                    <td><small>2025</small></td>
                                    <td><small>***********</small></td>
                                    <td><small><EMAIL></small></td>
                                </tr>
                                <tr>
                                    <td><small>1</small></td>
                                    <td><small>আলী হাসান</small></td>
                                    <td><small>REG2025003</small></td>
                                    <td><small>Arts</small></td>
                                    <td><small>বাবার নাম</small></td>
                                    <td><small>মায়ের নাম</small></td>
                                    <td><small>Male</small></td>
                                    <td><small>2005-03-10</small></td>
                                    <td><small>2025</small></td>
                                    <td><small>***********</small></td>
                                    <td><small><EMAIL></small></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ফিল্ড বিবরণ:</strong>
                        <ul class="mb-0 mt-2">
                            <li><strong>name:</strong> ছাত্রের নাম (প্রয়োজনীয়)</li>
                            <li><strong>roll:</strong> রোল নম্বর (CSV এ যেভাবে আছে সেভাবেই থাকবে, একই রোল বিভিন্ন গ্রুপে থাকতে পারে)</li>
                            <li><strong>regi:</strong> রেজিস্ট্রেশন নম্বর (অনন্য হতে হবে, খালি থাকলে অটো জেনারেট হবে)</li>
                            <li><strong>group:</strong> গ্রুপ (Science, Commerce, Arts, Vocational)</li>
                            <li><strong>gender:</strong> লিঙ্গ (Male, Female, Other)</li>
                            <li><strong>dob:</strong> জন্ম তারিখ (YYYY-MM-DD ফরম্যাটে)</li>
                            <li><strong>blood_group:</strong> রক্তের গ্রুপ (A+, A-, B+, B-, AB+, AB-, O+, O-)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reset Confirmation Modal -->
    <div class="modal fade" id="resetModal" tabindex="-1" aria-labelledby="resetModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="resetModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        সতর্কতা - সব ছাত্র-ছাত্রী মুছে ফেলুন
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>এই কাজটি অপরিবর্তনীয়!</strong>
                    </div>

                    <p class="mb-3">আপনি কি নিশ্চিত যে আপনি <strong>সব <?php echo $studentCount; ?> জন ছাত্র-ছাত্রীর তথ্য</strong> মুছে ফেলতে চান?</p>

                    <div class="bg-light p-3 rounded mb-3">
                        <h6 class="text-danger mb-2">
                            <i class="fas fa-info-circle me-2"></i>যা মুছে যাবে:
                        </h6>
                        <ul class="mb-0">
                            <li><strong><?php echo $studentCount; ?> জন</strong> ছাত্র-ছাত্রীর সব ব্যক্তিগত তথ্য</li>
                            <li>সব ছাত্র-ছাত্রীর প্রোফাইল ছবি</li>
                            <li>সব রোল ও রেজিস্ট্রেশন নম্বর</li>
                            <li>সব পরীক্ষার ফলাফল ও নম্বর</li>
                            <li>সব ফি পেমেন্ট রেকর্ড ও ইতিহাস</li>
                            <li>সব একাডেমিক রেকর্ড ও ডকুমেন্ট</li>
                        </ul>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="confirmReset">
                        <label class="form-check-label text-danger" for="confirmReset">
                            <strong>হ্যাঁ, আমি নিশ্চিত যে আমি সব ছাত্র-ছাত্রীর তথ্য মুছে ফেলতে চাই।</strong>
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> বাতিল
                    </button>
                    <form method="post" style="display: inline;" id="resetForm">
                        <input type="hidden" name="action" value="reset_all_students">
                        <button type="submit" class="btn btn-danger" id="confirmResetBtn" disabled>
                            <i class="fas fa-trash-alt me-1"></i> সব মুছে ফেলুন
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>

    <script>
        $(document).ready(function() {
            initializeFileUpload();
            initializeAutoComplete();
            initializeResetModal();
        });

        // File upload functionality
        function initializeFileUpload() {
            const uploadArea = $('#uploadArea');
            const fileInput = $('#csvFile');
            const fileInfo = $('#fileInfo');
            const submitBtn = $('#submitBtn');

            // Drag and drop events
            uploadArea.on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            uploadArea.on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelection(files[0]);
                }
            });

            // File input change
            fileInput.on('change', function() {
                if (this.files.length > 0) {
                    handleFileSelection(this.files[0]);
                }
            });

            // Click to select file
            uploadArea.on('click', function() {
                fileInput.click();
            });
        }

        function handleFileSelection(file) {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.csv')) {
                showAlert('শুধুমাত্র CSV ফাইল আপলোড করুন।', 'danger');
                return;
            }

            // Validate file size (10MB)
            const maxSize = 10 * 1024 * 1024;
            if (file.size > maxSize) {
                showAlert('ফাইলের আকার ১০ MB এর বেশি হতে পারবে না।', 'danger');
                return;
            }

            // Show file info
            $('#fileName').text(file.name);
            $('#fileSize').text(formatFileSize(file.size));
            $('#fileInfo').show();
            $('#submitBtn').prop('disabled', false);

            // Preview CSV content
            previewCSVContent(file);
        }

        function previewCSVContent(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const csv = e.target.result;
                const lines = csv.split('\n');

                if (lines.length > 0) {
                    const headers = parseCSVLine(lines[0]);
                    showFieldMapping(headers);
                }
            };
            reader.readAsText(file);
        }

        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
                const char = line[i];

                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }

            result.push(current.trim());
            return result;
        }

        function showFieldMapping(headers) {
            const dbFields = [
                'roll', 'name', 'regi', 'group', 'fname', 'mname', 'gender',
                'dob', 'session', 'sub1', 'sub2', 'sub3', '4th_sub', 'phone',
                'email', 'address', 'blood_group', 'religion', 'admission_date'
            ];

            const requiredFields = ['name'];
            let mappingHtml = '';

            headers.forEach((header, index) => {
                const cleanHeader = header.toLowerCase().trim();
                const isRequired = requiredFields.includes(cleanHeader);
                const isValid = dbFields.includes(cleanHeader);

                let statusIcon = '';
                let statusClass = '';

                if (isRequired && isValid) {
                    statusIcon = 'fas fa-check-circle';
                    statusClass = 'status-valid';
                } else if (isValid) {
                    statusIcon = 'fas fa-check';
                    statusClass = 'status-valid';
                } else if (isRequired) {
                    statusIcon = 'fas fa-exclamation-triangle';
                    statusClass = 'status-invalid';
                } else {
                    statusIcon = 'fas fa-question-circle';
                    statusClass = 'status-warning';
                }

                mappingHtml += `
                    <div class="mapping-row">
                        <div class="csv-column">${header}</div>
                        <div class="mapping-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="db-field">
                            <select class="form-select form-select-sm field-mapping-select" data-csv-index="${index}">
                                <option value="">-- নির্বাচন করুন --</option>
                                ${dbFields.map(field =>
                                    `<option value="${field}" ${field === cleanHeader ? 'selected' : ''}>${field}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="validation-status">
                            <i class="${statusIcon} status-icon ${statusClass}"></i>
                        </div>
                    </div>
                `;
            });

            $('#mappingContent').html(mappingHtml);
            $('#fieldMapping').show();

            // Initialize auto-complete for mapping selects
            initializeMappingAutoComplete();
        }

        function initializeMappingAutoComplete() {
            $('.field-mapping-select').each(function() {
                const select = $(this);
                const container = $('<div class="auto-complete-container"></div>');
                const input = $('<input type="text" class="form-control form-control-sm auto-complete-input">');
                const suggestions = $('<div class="auto-complete-suggestions"></div>');

                // Replace select with input
                select.hide();
                container.append(input).append(suggestions);
                select.after(container);

                // Set initial value
                const selectedValue = select.val();
                if (selectedValue) {
                    input.val(selectedValue);
                }

                // Auto-complete functionality
                input.on('input', function() {
                    const query = $(this).val().toLowerCase();
                    const options = select.find('option').not(':first');

                    suggestions.empty();

                    if (query.length > 0) {
                        let hasMatches = false;

                        options.each(function() {
                            const option = $(this);
                            const value = option.val();
                            const text = option.text();

                            if (value.toLowerCase().includes(query) || text.toLowerCase().includes(query)) {
                                const item = $(`<div class="auto-complete-item" data-value="${value}">${value}</div>`);
                                suggestions.append(item);
                                hasMatches = true;
                            }
                        });

                        if (hasMatches) {
                            suggestions.show();
                        } else {
                            suggestions.hide();
                        }
                    } else {
                        suggestions.hide();
                    }
                });

                // Handle suggestion selection
                suggestions.on('click', '.auto-complete-item', function() {
                    const value = $(this).data('value');
                    input.val(value);
                    select.val(value);
                    suggestions.hide();
                    updateValidationStatus(select);
                });

                // Hide suggestions when clicking outside
                $(document).on('click', function(e) {
                    if (!container.is(e.target) && container.has(e.target).length === 0) {
                        suggestions.hide();
                    }
                });

                // Keyboard navigation
                input.on('keydown', function(e) {
                    const items = suggestions.find('.auto-complete-item');
                    const active = suggestions.find('.auto-complete-item.active');

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (active.length === 0) {
                            items.first().addClass('active');
                        } else {
                            active.removeClass('active');
                            const next = active.next();
                            if (next.length > 0) {
                                next.addClass('active');
                            } else {
                                items.first().addClass('active');
                            }
                        }
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (active.length === 0) {
                            items.last().addClass('active');
                        } else {
                            active.removeClass('active');
                            const prev = active.prev();
                            if (prev.length > 0) {
                                prev.addClass('active');
                            } else {
                                items.last().addClass('active');
                            }
                        }
                    } else if (e.key === 'Enter') {
                        e.preventDefault();
                        if (active.length > 0) {
                            active.click();
                        }
                    } else if (e.key === 'Escape') {
                        suggestions.hide();
                    }
                });

                // Update select value when input changes
                input.on('blur', function() {
                    const value = $(this).val();
                    if (select.find(`option[value="${value}"]`).length > 0) {
                        select.val(value);
                    } else {
                        select.val('');
                    }
                    updateValidationStatus(select);
                });
            });
        }

        function updateValidationStatus(select) {
            const mappingRow = select.closest('.mapping-row');
            const statusIcon = mappingRow.find('.status-icon');
            const value = select.val();
            const csvIndex = select.data('csv-index');

            // Update status based on mapping
            if (value === 'name') {
                statusIcon.removeClass().addClass('fas fa-check-circle status-icon status-valid');
            } else if (value) {
                statusIcon.removeClass().addClass('fas fa-check status-icon status-valid');
            } else {
                statusIcon.removeClass().addClass('fas fa-question-circle status-icon status-warning');
            }
        }

        function initializeAutoComplete() {
            // General auto-complete functionality for other inputs
            $('.auto-complete-input').each(function() {
                const input = $(this);
                const suggestions = input.siblings('.auto-complete-suggestions');

                input.on('input', function() {
                    const query = $(this).val().toLowerCase();
                    // Implement specific auto-complete logic here
                });
            });
        }

        function clearFile() {
            $('#csvFile').val('');
            $('#fileInfo').hide();
            $('#fieldMapping').hide();
            $('#submitBtn').prop('disabled', true);
            $('#progressContainer').hide();
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // Remove existing alerts
            $('.alert').remove();

            // Add new alert
            $('.container-fluid').prepend(alertHtml);

            // Auto-hide after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }

        // Form submission with progress
        $('#uploadForm').on('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const progressContainer = $('#progressContainer');
            const progressBar = $('#progressBar');
            const progressText = $('#progressText');
            const submitBtn = $('#submitBtn');

            // Show progress
            progressContainer.show();
            submitBtn.prop('disabled', true);

            // Simulate progress (since we can't track actual CSV processing progress)
            let progress = 0;
            const progressInterval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                progressBar.css('width', progress + '%');
                progressText.text(Math.round(progress) + '%');
            }, 200);

            // Submit form
            $.ajax({
                url: '',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    clearInterval(progressInterval);
                    progressBar.css('width', '100%');
                    progressText.text('100%');

                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                },
                error: function() {
                    clearInterval(progressInterval);
                    showAlert('আপলোড করতে সমস্যা হয়েছে। আবার চেষ্টা করুন।', 'danger');
                    submitBtn.prop('disabled', false);
                    progressContainer.hide();
                }
            });
        });

        // Keyboard shortcuts
        $(document).on('keydown', function(e) {
            // Ctrl+U for upload
            if (e.ctrlKey && e.key === 'u') {
                e.preventDefault();
                $('#csvFile').click();
            }

            // Ctrl+D for download template
            if (e.ctrlKey && e.key === 'd') {
                e.preventDefault();
                $('form input[name="action"][value="download_template"]').closest('form').submit();
            }
        });

        // Tooltip initialization
        $('[data-bs-toggle="tooltip"]').tooltip();

        // Auto-save form data to localStorage
        function saveFormData() {
            const formData = {};
            $('#uploadForm input, #uploadForm select').each(function() {
                const input = $(this);
                if (input.attr('type') !== 'file') {
                    formData[input.attr('name')] = input.val();
                }
            });
            localStorage.setItem('bulkUploadFormData', JSON.stringify(formData));
        }

        function loadFormData() {
            const savedData = localStorage.getItem('bulkUploadFormData');
            if (savedData) {
                const formData = JSON.parse(savedData);
                Object.keys(formData).forEach(function(key) {
                    $(`#uploadForm [name="${key}"]`).val(formData[key]);
                });
            }
        }

        // Load saved form data on page load
        loadFormData();

        // Save form data on input change
        $('#uploadForm').on('input change', 'input, select', function() {
            saveFormData();
        });

        // Reset modal functionality
        function initializeResetModal() {
            const confirmCheckbox = $('#confirmReset');
            const confirmBtn = $('#confirmResetBtn');
            const resetForm = $('#resetForm');

            // Enable/disable confirm button based on checkbox
            confirmCheckbox.on('change', function() {
                confirmBtn.prop('disabled', !this.checked);
            });

            // Reset form submission with additional confirmation
            resetForm.on('submit', function(e) {
                e.preventDefault();

                if (!confirmCheckbox.is(':checked')) {
                    showAlert('দয়া করে নিশ্চিতকরণ চেকবক্স টিক দিন।', 'warning');
                    return;
                }

                // Final confirmation
                const finalConfirm = confirm('আপনি কি সত্যিই সব ছাত্র-ছাত্রীর তথ্য মুছে ফেলতে চান? এই কাজটি আর ফেরত নেওয়া যাবে না।');

                if (finalConfirm) {
                    // Show loading state
                    confirmBtn.html('<i class="fas fa-spinner fa-spin me-1"></i> মুছে ফেলা হচ্ছে...');
                    confirmBtn.prop('disabled', true);

                    // Submit form
                    this.submit();
                }
            });

            // Reset modal state when closed
            $('#resetModal').on('hidden.bs.modal', function() {
                confirmCheckbox.prop('checked', false);
                confirmBtn.prop('disabled', true);
                confirmBtn.html('<i class="fas fa-trash-alt me-1"></i> সব মুছে ফেলুন');
            });
        }
    </script>
</body>
</html>
